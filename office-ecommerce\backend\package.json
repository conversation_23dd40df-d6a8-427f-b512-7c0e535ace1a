{"name": "office-ecommerce-backend", "version": "1.0.0", "description": "Backend API for Office Furniture E-commerce with Inventory Management", "main": "server.js", "scripts": {"start": "node server.js", "dev": "nodemon server.js", "test": "jest", "migrate": "node scripts/migrate.js", "setup-db": "node scripts/setup-database.js", "db:setup": "node scripts/setup-database.js setup", "db:user": "node scripts/setup-database.js user", "db:all": "node scripts/setup-database.js all"}, "keywords": ["office-furniture", "ecommerce", "inventory-management", "nodejs", "express", "mssql"], "author": "DesignXcel", "license": "MIT", "dependencies": {"axios": "^1.6.2", "bcryptjs": "^2.4.3", "compression": "^1.7.4", "cors": "^2.8.5", "dotenv": "^16.3.1", "express": "^4.18.2", "express-rate-limit": "^7.1.5", "express-validator": "^7.0.1", "helmet": "^7.1.0", "joi": "^17.11.0", "jsonwebtoken": "^9.0.2", "moment": "^2.29.4", "morgan": "^1.10.0", "mssql": "^10.0.1", "multer": "^1.4.5-lts.1", "nodemailer": "^6.9.7", "socket.io": "^4.8.1", "socket.io-client": "^4.7.2", "sqlite": "^5.1.1", "sqlite3": "^5.1.6", "uuid": "^9.0.1"}, "devDependencies": {"@types/jest": "^29.5.8", "jest": "^29.7.0", "nodemon": "^3.0.2", "supertest": "^6.3.3"}, "engines": {"node": ">=16.0.0"}}