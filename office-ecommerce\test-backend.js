const axios = require('axios');

/**
 * Simple Backend Test Script
 * Tests if the backend is running and accessible
 */

const testBackend = async () => {
  console.log('🔍 Testing backend connectivity...\n');
  
  const tests = [
    {
      name: 'Root endpoint',
      url: 'http://localhost:5001/',
      expected: 'success'
    },
    {
      name: 'Health check',
      url: 'http://localhost:5001/health',
      expected: 'status'
    },
    {
      name: 'API Health check',
      url: 'http://localhost:5001/api/health',
      expected: 'success'
    },
    {
      name: 'Products endpoint',
      url: 'http://localhost:5001/api/products',
      expected: 'data'
    }
  ];
  
  let passedTests = 0;
  
  for (const test of tests) {
    try {
      console.log(`Testing ${test.name}...`);
      const response = await axios.get(test.url, { timeout: 5000 });
      
      if (response.status === 200) {
        console.log(`✅ ${test.name} - OK (${response.status})`);
        passedTests++;
      } else {
        console.log(`⚠️ ${test.name} - Unexpected status: ${response.status}`);
      }
    } catch (error) {
      if (error.code === 'ECONNREFUSED') {
        console.log(`❌ ${test.name} - Connection refused (server not running?)`);
      } else if (error.response) {
        console.log(`⚠️ ${test.name} - HTTP ${error.response.status}: ${error.response.statusText}`);
      } else {
        console.log(`❌ ${test.name} - ${error.message}`);
      }
    }
  }
  
  console.log(`\n📊 Test Results: ${passedTests}/${tests.length} tests passed\n`);
  
  if (passedTests === 0) {
    console.log('❌ Backend is not running or not accessible');
    console.log('💡 Try running: cd backend && npm start');
  } else if (passedTests < tests.length) {
    console.log('⚠️ Backend is partially working');
    console.log('💡 Some endpoints may have issues');
  } else {
    console.log('✅ Backend is working correctly!');
  }
  
  return passedTests === tests.length;
};

// Run the test
testBackend().catch(console.error);
