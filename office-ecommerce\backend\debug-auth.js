const { connectDB, executeQuery } = require('./config/database');
const bcrypt = require('bcryptjs');

async function debugAuth() {
  try {
    console.log('Testing database authentication...');

    // Connect to database first
    await connectDB();
    console.log('Database connected');
    
    const email = '<EMAIL>';
    const password = 'admin123';
    
    // Query the database for the user
    const query = `
      SELECT UserID, Email, PasswordHash, FirstName, LastName, Role, IsActive, EmailVerified
      FROM Users 
      WHERE Email = @email AND IsActive = 1
    `;
    
    console.log('Executing query:', query);
    console.log('With email:', email);
    
    const result = await executeQuery(query, { email });
    
    console.log('Query result:', result.recordset);
    
    if (!result.recordset || result.recordset.length === 0) {
      console.log('❌ No user found');
      return;
    }

    const user = result.recordset[0];
    console.log('User found:', {
      UserID: user.UserID,
      Email: user.Email,
      Role: user.Role,
      IsActive: user.IsActive,
      EmailVerified: user.EmailVerified
    });
    
    console.log('Stored hash:', user.PasswordHash);
    console.log('Testing password:', password);

    // Compare password with hash
    const isPasswordValid = await bcrypt.compare(password, user.PasswordHash);
    console.log('Password valid:', isPasswordValid);
    
  } catch (error) {
    console.error('Debug error:', error);
  }
}

debugAuth();
