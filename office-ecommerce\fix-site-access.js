const { spawn } = require('child_process');
const path = require('path');
const fs = require('fs');
const axios = require('axios');

/**
 * Fix Site Access Script
 * Comprehensive fix for "site can't be reached" error
 */

console.log('🔧 Fixing "Site Can\'t Be Reached" Error...\n');

// Step 1: Check and install dependencies
const checkDependencies = async () => {
  console.log('📦 Checking dependencies...');
  
  const backendPackageJson = path.join(__dirname, 'backend', 'package.json');
  const frontendPackageJson = path.join(__dirname, 'frontend', 'package.json');
  const backendNodeModules = path.join(__dirname, 'backend', 'node_modules');
  const frontendNodeModules = path.join(__dirname, 'frontend', 'node_modules');
  
  const needsInstall = [];
  
  if (!fs.existsSync(backendNodeModules)) {
    needsInstall.push('backend');
  }
  
  if (!fs.existsSync(frontendNodeModules)) {
    needsInstall.push('frontend');
  }
  
  if (needsInstall.length > 0) {
    console.log(`⚠️ Missing dependencies in: ${needsInstall.join(', ')}`);
    
    for (const dir of needsInstall) {
      console.log(`📦 Installing ${dir} dependencies...`);
      
      const installProcess = spawn('npm', ['install'], {
        cwd: path.join(__dirname, dir),
        stdio: 'pipe',
        shell: true
      });
      
      await new Promise((resolve, reject) => {
        installProcess.on('close', (code) => {
          if (code === 0) {
            console.log(`✅ ${dir} dependencies installed`);
            resolve();
          } else {
            console.log(`❌ Failed to install ${dir} dependencies`);
            reject(new Error(`npm install failed in ${dir}`));
          }
        });
      });
    }
  } else {
    console.log('✅ Dependencies are installed');
  }
};

// Step 2: Fix port configuration
const fixPortConfiguration = () => {
  console.log('🔧 Fixing port configuration...');
  
  // Fix backend port
  const backendEnvPath = path.join(__dirname, 'backend', '.env');
  const backendEnvContent = `# Fixed Backend Configuration
PORT=5001
NODE_ENV=development
FRONTEND_URL=http://localhost:3000

# JWT Configuration
JWT_SECRET=office-ecommerce-jwt-secret-key-2024
JWT_EXPIRES_IN=24h

# Database Configuration (disabled for now)
DB_ENABLED=false

# Logging
LOG_LEVEL=info
LOG_FILE=logs/app.log

# PayMongo Configuration
PAYMONGO_SECRET_KEY=sk_test_x4JDky9QoB7AomXCvPYdJs5W
PAYMONGO_PUBLIC_KEY=pk_test_MdQNPhNfqdixDE7V8qW9zbxG
`;
  
  fs.writeFileSync(backendEnvPath, backendEnvContent);
  
  // Fix frontend port
  const frontendEnvPath = path.join(__dirname, 'frontend', '.env');
  const frontendEnvContent = `# Fixed Frontend Configuration
REACT_APP_API_URL=http://localhost:5001
REACT_APP_WEBSOCKET_URL=http://localhost:5001
REACT_APP_ENVIRONMENT=development
REACT_APP_ENABLE_3D_CONFIGURATOR=true
REACT_APP_ENABLE_REAL_TIME_UPDATES=true
REACT_APP_ENABLE_ADMIN_DASHBOARD=true
REACT_APP_ENABLE_PAYMENT_PROCESSING=true
REACT_APP_PAYMONGO_PUBLIC_KEY=pk_test_MdQNPhNfqdixDE7V8qW9zbxG
GENERATE_SOURCEMAP=true
REACT_APP_DEBUG_MODE=true
`;
  
  fs.writeFileSync(frontendEnvPath, frontendEnvContent);
  
  console.log('✅ Port configuration fixed');
};

// Step 3: Start backend with error handling
const startBackendSafely = () => {
  return new Promise((resolve) => {
    console.log('🚀 Starting backend server...');
    
    const backendDir = path.join(__dirname, 'backend');
    const backendProcess = spawn('node', ['server.js'], {
      cwd: backendDir,
      stdio: 'pipe',
      shell: true
    });

    let serverStarted = false;

    backendProcess.stdout.on('data', (data) => {
      const output = data.toString().trim();
      if (output) {
        console.log(`[Backend] ${output}`);
        
        if (output.includes('Server running') || output.includes('running on port')) {
          if (!serverStarted) {
            serverStarted = true;
            console.log('✅ Backend server is running!');
            resolve(backendProcess);
          }
        }
      }
    });

    backendProcess.stderr.on('data', (data) => {
      const output = data.toString().trim();
      if (output && !output.includes('DeprecationWarning')) {
        console.log(`[Backend] ${output}`);
      }
    });

    backendProcess.on('close', (code) => {
      if (code !== 0) {
        console.log(`⚠️ Backend exited with code ${code}`);
      }
    });

    // Timeout fallback
    setTimeout(() => {
      if (!serverStarted) {
        console.log('⚠️ Backend may be starting (timeout reached)');
        resolve(backendProcess);
      }
    }, 20000);
  });
};

// Step 4: Test backend connectivity
const testBackendConnectivity = async () => {
  console.log('🔍 Testing backend connectivity...');
  
  const endpoints = [
    'http://localhost:5001/',
    'http://localhost:5001/health',
    'http://localhost:5001/api/health'
  ];
  
  for (let attempt = 1; attempt <= 5; attempt++) {
    console.log(`⏳ Attempt ${attempt}/5...`);
    
    for (const endpoint of endpoints) {
      try {
        const response = await axios.get(endpoint, { timeout: 3000 });
        if (response.status === 200) {
          console.log(`✅ ${endpoint} - OK`);
          return true;
        }
      } catch (error) {
        console.log(`❌ ${endpoint} - ${error.code || error.message}`);
      }
    }
    
    if (attempt < 5) {
      await new Promise(resolve => setTimeout(resolve, 3000));
    }
  }
  
  return false;
};

// Step 5: Start frontend
const startFrontend = () => {
  return new Promise((resolve) => {
    console.log('🚀 Starting frontend server...');
    
    const frontendDir = path.join(__dirname, 'frontend');
    const frontendProcess = spawn('npm', ['start'], {
      cwd: frontendDir,
      stdio: 'pipe',
      shell: true,
      env: { ...process.env, BROWSER: 'none' }
    });

    let frontendStarted = false;

    frontendProcess.stdout.on('data', (data) => {
      const output = data.toString().trim();
      if (output) {
        console.log(`[Frontend] ${output}`);
        
        if (output.includes('webpack compiled') || output.includes('Local:') || output.includes('localhost:3000')) {
          if (!frontendStarted) {
            frontendStarted = true;
            console.log('✅ Frontend server is running!');
            resolve(frontendProcess);
          }
        }
      }
    });

    frontendProcess.stderr.on('data', (data) => {
      const output = data.toString().trim();
      if (output && !output.includes('DeprecationWarning')) {
        console.log(`[Frontend] ${output}`);
      }
    });

    // Timeout fallback
    setTimeout(() => {
      if (!frontendStarted) {
        console.log('⚠️ Frontend may be starting (timeout reached)');
        resolve(frontendProcess);
      }
    }, 45000);
  });
};

// Main execution
const main = async () => {
  try {
    console.log('🎯 Office E-commerce Site Access Fix\n');
    
    // Step 1: Check dependencies
    await checkDependencies();
    
    // Step 2: Fix configuration
    fixPortConfiguration();
    
    // Step 3: Start backend
    const backendProcess = await startBackendSafely();
    
    // Step 4: Test backend
    const backendWorking = await testBackendConnectivity();
    
    if (backendWorking) {
      console.log('✅ Backend is working correctly!');
      
      // Step 5: Start frontend
      const frontendProcess = await startFrontend();
      
      console.log('\n🎉 Site Access Fixed!');
      console.log('🌐 Frontend: http://localhost:3000');
      console.log('🔧 Backend: http://localhost:5001');
      console.log('❤️ Health: http://localhost:5001/health');
      
      // Handle shutdown
      process.on('SIGINT', () => {
        console.log('\n🛑 Shutting down...');
        backendProcess.kill('SIGTERM');
        frontendProcess.kill('SIGTERM');
        setTimeout(() => process.exit(0), 2000);
      });
      
      // Keep alive
      process.stdin.resume();
      
    } else {
      console.log('❌ Backend is not responding');
      console.log('🔧 Try running: node start-backend-only.js');
    }
    
  } catch (error) {
    console.error('❌ Fix failed:', error.message);
    console.log('\n🔧 Manual steps:');
    console.log('1. cd backend && npm install && npm start');
    console.log('2. cd frontend && npm install && npm start');
    console.log('3. Check http://localhost:5001/health');
    console.log('4. Check http://localhost:3000');
    process.exit(1);
  }
};

// Run the fix
main();
