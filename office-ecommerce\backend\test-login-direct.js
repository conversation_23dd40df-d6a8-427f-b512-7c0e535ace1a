const AuthService = require('./services/authService');
const { connectDB } = require('./config/database');

async function testLogin() {
  try {
    console.log('Testing login functionality...');
    
    // Connect to database
    await connectDB();
    console.log('✅ Database connected');
    
    // Create auth service instance
    const authService = new AuthService();
    
    // Test with admin credentials
    console.log('\nTesting admin login...');
    const adminResult = await authService.validateCredentials('<EMAIL>', 'admin123');
    console.log('Admin login result:', adminResult);
    
    // Test with manager credentials
    console.log('\nTesting manager login...');
    const managerResult = await authService.validateCredentials('<EMAIL>', 'admin123');
    console.log('Manager login result:', managerResult);
    
    // Test with invalid credentials
    console.log('\nTesting invalid login...');
    const invalidResult = await authService.validateCredentials('<EMAIL>', 'wrongpassword');
    console.log('Invalid login result:', invalidResult);
    
  } catch (error) {
    console.error('❌ Test failed:', error);
  }
  
  process.exit(0);
}

testLogin();
