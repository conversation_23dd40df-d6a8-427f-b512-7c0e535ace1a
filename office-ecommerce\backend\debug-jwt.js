const jwt = require('jsonwebtoken');

// Test JWT token generation and decoding
async function debugJWT() {
  try {
    // Simulate the user data from database
    const userData = {
      UserID: '12345',
      Email: '<EMAIL>',
      Name: 'Admin User',
      FirstName: 'Admin',
      LastName: 'User',
      Role: 'Admin',
      IsActive: true,
      EmailVerified: true
    };

    console.log('Original user data:', userData);

    // Generate token like the AuthService does
    const token = jwt.sign(
      { 
        id: userData.UserID, 
        email: userData.Email, 
        role: userData.Role,
        name: userData.Name
      },
      process.env.JWT_SECRET || 'your-secret-key',
      { expiresIn: process.env.JWT_EXPIRES_IN || '1h' }
    );

    console.log('Generated token:', token);

    // Decode the token
    const decoded = jwt.decode(token);
    console.log('Decoded token payload:', decoded);

    // Verify the token
    const verified = jwt.verify(token, process.env.JWT_SECRET || 'your-secret-key');
    console.log('Verified token payload:', verified);

  } catch (error) {
    console.error('JWT debug error:', error);
  }
}

debugJWT();
