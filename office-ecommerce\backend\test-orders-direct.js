const axios = require('axios');

async function testOrdersDirect() {
  try {
    console.log('🔍 Testing Orders Endpoint Directly...');

    console.log('\n🔍 Testing /api/orders endpoint without auth...');
    try {
      const response = await axios.get('http://localhost:8000/api/orders?page=1&limit=10');
      console.log('✅ Orders endpoint working');
      console.log('Response:', JSON.stringify(response.data, null, 2));
    } catch (error) {
      console.log('❌ Orders endpoint failed:', error.response?.status, error.response?.data?.message || error.message);
      if (error.response?.data) {
        console.log('Full error response:', JSON.stringify(error.response.data, null, 2));
      }
    }

    console.log('\n✅ Test completed!');

  } catch (error) {
    console.error('❌ Test failed:', error.message);
    console.error('Stack:', error.stack);
  }
}

// Run the test
testOrdersDirect();
