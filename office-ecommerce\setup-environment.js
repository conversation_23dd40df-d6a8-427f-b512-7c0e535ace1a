const fs = require('fs');
const path = require('path');

/**
 * Environment Setup Script
 * Creates and validates environment files for both backend and frontend
 */

const backendEnvTemplate = `# Database Configuration - Using SQL Server Authentication
DB_USER=OfficeEcommerceUser
DB_PASSWORD=OfficeApp123!
DB_SERVER=DESKTOP-DPNS718\\\\SQLEXPRESS
DB_NAME=OfficeEcommerce
DB_PORT=1433
DB_ENCRYPT=false
DB_TRUST_CERT=true

# JWT Configuration
JWT_SECRET=office-ecommerce-jwt-secret-key-2024-production-ready
JWT_REFRESH_SECRET=office-ecommerce-refresh-secret-key-2024-production-ready
JWT_EXPIRES_IN=24h
JWT_REFRESH_EXPIRES_IN=7d

# Server Configuration
PORT=5001
NODE_ENV=development
FRONTEND_URL=http://localhost:3000

# Email Configuration (for notifications)
EMAIL_HOST=smtp.gmail.com
EMAIL_PORT=587
EMAIL_USER=<EMAIL>
EMAIL_PASSWORD=your-app-password
EMAIL_FROM=<EMAIL>

# File Upload Configuration
UPLOAD_PATH=uploads
MAX_FILE_SIZE=5242880

# Inventory Configuration
LOW_STOCK_THRESHOLD=10
CRITICAL_STOCK_THRESHOLD=5
AUTO_REORDER_ENABLED=true

# PayMongo Configuration
PAYMONGO_SECRET_KEY=sk_test_x4JDky9QoB7AomXCvPYdJs5W
PAYMONGO_PUBLIC_KEY=pk_test_MdQNPhNfqdixDE7V8qW9zbxG
PAYMONGO_WEBHOOK_SECRET=whsk_u8NbXEh3KqCMxgFZJ3h46yWH

# Logging
LOG_LEVEL=info
LOG_FILE=logs/app.log

# Security Configuration
BCRYPT_ROUNDS=12
RATE_LIMIT_WINDOW=900000
RATE_LIMIT_MAX=100
`;

const frontendEnvTemplate = `# Frontend Environment Configuration
# This file contains environment variables for the React frontend application

# API Configuration
REACT_APP_API_URL=http://localhost:5001
REACT_APP_API_VERSION=v1

# WebSocket Configuration
REACT_APP_WEBSOCKET_URL=http://localhost:5001

# Application Configuration
REACT_APP_NAME=Office Furniture E-commerce
REACT_APP_VERSION=1.0.0
REACT_APP_ENVIRONMENT=development

# Feature Flags
REACT_APP_ENABLE_3D_CONFIGURATOR=true
REACT_APP_ENABLE_REAL_TIME_UPDATES=true
REACT_APP_ENABLE_ADMIN_DASHBOARD=true
REACT_APP_ENABLE_PAYMENT_PROCESSING=true

# PayMongo Configuration (Frontend Public Keys)
REACT_APP_PAYMONGO_PUBLIC_KEY=pk_test_MdQNPhNfqdixDE7V8qW9zbxG

# Currency and Localization
REACT_APP_DEFAULT_CURRENCY=PHP
REACT_APP_DEFAULT_LANGUAGE=en
REACT_APP_SUPPORTED_CURRENCIES=PHP,USD
REACT_APP_SUPPORTED_LANGUAGES=en,fil

# Build Configuration
GENERATE_SOURCEMAP=true

# Development Configuration
REACT_APP_DEBUG_MODE=true
REACT_APP_LOG_LEVEL=debug

# Static Asset Configuration
REACT_APP_ASSETS_URL=/
REACT_APP_MODELS_PATH=/models
REACT_APP_IMAGES_PATH=/images
REACT_APP_3D_MODELS_PATH=/models
`;

function createEnvironmentFile(filePath, content, description) {
  try {
    // Check if file already exists
    if (fs.existsSync(filePath)) {
      console.log(`⚠️  ${description} already exists at ${filePath}`);
      console.log('   Skipping creation to avoid overwriting existing configuration');
      return false;
    }

    // Create directory if it doesn't exist
    const dir = path.dirname(filePath);
    if (!fs.existsSync(dir)) {
      fs.mkdirSync(dir, { recursive: true });
    }

    // Write the file
    fs.writeFileSync(filePath, content);
    console.log(`✅ Created ${description} at ${filePath}`);
    return true;
  } catch (error) {
    console.error(`❌ Failed to create ${description}:`, error.message);
    return false;
  }
}

function validateEnvironmentFile(filePath, requiredVars, description) {
  try {
    if (!fs.existsSync(filePath)) {
      console.error(`❌ ${description} not found at ${filePath}`);
      return false;
    }

    const content = fs.readFileSync(filePath, 'utf8');
    const missingVars = [];

    requiredVars.forEach(varName => {
      const regex = new RegExp(`^${varName}=`, 'm');
      if (!regex.test(content)) {
        missingVars.push(varName);
      }
    });

    if (missingVars.length > 0) {
      console.error(`❌ ${description} is missing required variables:`);
      missingVars.forEach(varName => {
        console.error(`   - ${varName}`);
      });
      return false;
    }

    console.log(`✅ ${description} validation passed`);
    return true;
  } catch (error) {
    console.error(`❌ Failed to validate ${description}:`, error.message);
    return false;
  }
}

function setupEnvironment() {
  console.log('🔧 Setting up environment configuration...\n');

  const backendEnvPath = path.join(__dirname, 'backend', '.env');
  const frontendEnvPath = path.join(__dirname, 'frontend', '.env');

  // Create environment files
  const backendCreated = createEnvironmentFile(
    backendEnvPath, 
    backendEnvTemplate, 
    'Backend environment file'
  );

  const frontendCreated = createEnvironmentFile(
    frontendEnvPath, 
    frontendEnvTemplate, 
    'Frontend environment file'
  );

  console.log('');

  // Validate environment files
  const requiredBackendVars = [
    'DB_SERVER', 'DB_NAME', 'DB_USER', 'DB_PASSWORD',
    'JWT_SECRET', 'PORT', 'NODE_ENV',
    'PAYMONGO_SECRET_KEY', 'PAYMONGO_PUBLIC_KEY'
  ];

  const requiredFrontendVars = [
    'REACT_APP_API_URL', 'REACT_APP_WEBSOCKET_URL',
    'REACT_APP_PAYMONGO_PUBLIC_KEY'
  ];

  const backendValid = validateEnvironmentFile(
    backendEnvPath, 
    requiredBackendVars, 
    'Backend environment'
  );

  const frontendValid = validateEnvironmentFile(
    frontendEnvPath, 
    requiredFrontendVars, 
    'Frontend environment'
  );

  console.log('\n📋 Environment Setup Summary:');
  console.log(`   Backend environment: ${backendValid ? '✅ Valid' : '❌ Invalid'}`);
  console.log(`   Frontend environment: ${frontendValid ? '✅ Valid' : '❌ Invalid'}`);

  if (backendCreated || frontendCreated) {
    console.log('\n📝 Next Steps:');
    console.log('   1. Review the created environment files');
    console.log('   2. Update database credentials if needed');
    console.log('   3. Configure PayMongo API keys for payment processing');
    console.log('   4. Set up email configuration for notifications');
  }

  return backendValid && frontendValid;
}

// Run setup if called directly
if (require.main === module) {
  setupEnvironment();
}

module.exports = {
  setupEnvironment,
  createEnvironmentFile,
  validateEnvironmentFile
};
