const sql = require('mssql');

// Test different connection configurations
const configs = [
  {
    name: 'Simple localhost connection',
    config: {
      server: 'localhost',
      database: 'OfficeEcommerce',
      options: {
        encrypt: false,
        trustServerCertificate: true,
        integratedSecurity: true,
        requestTimeout: 30000,
        connectionTimeout: 30000
      }
    }
  },
  {
    name: 'Localhost with instance name',
    config: {
      server: 'localhost\\SQLEXPRESS',
      database: 'OfficeEcommerce',
      options: {
        encrypt: false,
        trustServerCertificate: true,
        integratedSecurity: true,
        requestTimeout: 30000,
        connectionTimeout: 30000
      }
    }
  },
  {
    name: 'Computer name with instance',
    config: {
      server: 'DESKTOP-DPNS718\\SQLEXPRESS',
      database: 'OfficeEcommerce',
      options: {
        encrypt: false,
        trustServerCertificate: true,
        integratedSecurity: true,
        requestTimeout: 30000,
        connectionTimeout: 30000
      }
    }
  }
];

async function testConnection(config) {
  console.log(`\nTesting: ${config.name}`);
  console.log('Config:', JSON.stringify(config.config, null, 2));
  
  try {
    const pool = await sql.connect(config.config);
    const result = await pool.request().query('SELECT @@VERSION as version, DB_NAME() as database');
    console.log('✅ SUCCESS!');
    console.log('Version:', result.recordset[0].version.substring(0, 100) + '...');
    console.log('Database:', result.recordset[0].database);
    await pool.close();
    return true;
  } catch (error) {
    console.log('❌ FAILED:', error.message);
    return false;
  }
}

async function main() {
  console.log('Testing SQL Server connections...\n');
  
  for (const config of configs) {
    const success = await testConnection(config);
    if (success) {
      console.log('\n🎉 Found working configuration!');
      break;
    }
  }
}

main().catch(console.error);
