const { connectDB } = require('./config/database');
const Order = require('./models/Order');

async function testOrdersSimple() {
  try {
    console.log('🔍 Testing Orders Database Connection...');

    // Connect to database
    console.log('\n📊 Connecting to database...');
    await connectDB();
    console.log('✅ Database connected');

    // Create order model instance
    const orderModel = new Order();

    // Test simple query first
    console.log('\n🔍 Testing simple orders query...');
    try {
      const simpleResult = await orderModel.executeQuery('SELECT COUNT(*) as count FROM Orders');
      console.log('✅ Simple query successful');
      console.log('Orders count:', simpleResult[0]?.count || 0);
    } catch (error) {
      console.log('❌ Simple query failed:', error.message);
    }

    // Test the pagination method
    console.log('\n🔍 Testing getOrdersWithPagination method...');
    try {
      const result = await orderModel.getOrdersWithPagination({
        page: 1,
        limit: 10
      });
      console.log('✅ Pagination method successful');
      console.log('Result:', JSON.stringify(result, null, 2));
    } catch (error) {
      console.log('❌ Pagination method failed:', error.message);
      console.log('Stack:', error.stack);
    }

    console.log('\n✅ Test completed!');

  } catch (error) {
    console.error('❌ Test failed:', error.message);
    console.error('Stack:', error.stack);
  }
}

// Run the test
testOrdersSimple();
