const axios = require('axios');

async function testLoginEndpoint() {
  try {
    console.log('🔍 Testing Login Endpoint...');

    console.log('\n🔐 Testing login...');
    try {
      const response = await axios.post('http://localhost:8000/api/auth/login', {
        email: '<EMAIL>',
        password: 'admin123'
      });
      console.log('✅ Login successful');
      console.log('Response:', JSON.stringify(response.data, null, 2));
    } catch (error) {
      console.log('❌ Login failed:', error.response?.status, error.response?.data?.message || error.message);
      if (error.response?.data) {
        console.log('Full error response:', JSON.stringify(error.response.data, null, 2));
      }
    }

    console.log('\n✅ Test completed!');

  } catch (error) {
    console.error('❌ Test failed:', error.message);
    console.error('Stack:', error.stack);
  }
}

// Run the test
testLoginEndpoint();
