const { Server } = require('socket.io');
const jwt = require('jsonwebtoken');
const logger = require('../utils/logger');

class WebSocketService {
  constructor() {
    this.io = null;
    this.connectedUsers = new Map();
  }

  initialize(server) {
    this.io = new Server(server, {
      cors: {
        origin: [
          process.env.FRONTEND_URL || "http://localhost:3000",
          "http://localhost:3000",
          "http://localhost:3001"
        ],
        methods: ["GET", "POST"],
        credentials: true
      },
      transports: ['websocket', 'polling']
    });

    // Authentication middleware for socket connections
    this.io.use((socket, next) => {
      const token = socket.handshake.auth.token;
      
      if (!token) {
        return next(new Error('Authentication error'));
      }

      try {
        const decoded = jwt.verify(token, process.env.JWT_SECRET || 'your-secret-key');
        socket.userId = decoded.id;
        socket.userRole = decoded.role;
        next();
      } catch (err) {
        next(new Error('Authentication error'));
      }
    });

    this.io.on('connection', (socket) => {
      logger.info(`User connected: ${socket.userId} (${socket.userRole})`);
      
      // Store connected user
      this.connectedUsers.set(socket.userId, {
        socketId: socket.id,
        role: socket.userRole,
        connectedAt: new Date()
      });

      // Join role-based rooms
      if (socket.userRole === 'Admin' || socket.userRole === 'Employee') {
        socket.join('admin');
        logger.info(`User ${socket.userId} joined admin room`);
      }

      // Handle inventory updates subscription
      socket.on('subscribe:inventory', () => {
        if (socket.userRole === 'Admin' || socket.userRole === 'Employee') {
          socket.join('inventory-updates');
          logger.info(`User ${socket.userId} subscribed to inventory updates`);
        }
      });

      // Handle order updates subscription
      socket.on('subscribe:orders', () => {
        if (socket.userRole === 'Admin' || socket.userRole === 'Employee') {
          socket.join('order-updates');
          logger.info(`User ${socket.userId} subscribed to order updates`);
        }
      });

      // Handle dashboard updates subscription
      socket.on('subscribe:dashboard', () => {
        if (socket.userRole === 'Admin' || socket.userRole === 'Employee') {
          socket.join('dashboard-updates');
          logger.info(`User ${socket.userId} subscribed to dashboard updates`);
        }
      });

      // Handle product updates subscription
      socket.on('subscribe:products', () => {
        if (socket.userRole === 'Admin' || socket.userRole === 'Employee') {
          socket.join('product-updates');
          logger.info(`User ${socket.userId} subscribed to product updates`);
        }
      });

      // Handle generic subscribe events (for backward compatibility)
      socket.on('subscribe', (channel) => {
        if (socket.userRole === 'Admin' || socket.userRole === 'Employee') {
          switch (channel) {
            case 'inventory':
              socket.join('inventory-updates');
              break;
            case 'orders':
              socket.join('order-updates');
              break;
            case 'dashboard':
              socket.join('dashboard-updates');
              break;
            case 'products':
              socket.join('product-updates');
              break;
          }
          logger.info(`User ${socket.userId} subscribed to ${channel} updates`);
        }
      });

      // Handle disconnection
      socket.on('disconnect', () => {
        logger.info(`User disconnected: ${socket.userId}`);
        this.connectedUsers.delete(socket.userId);
      });

      // Send initial connection confirmation
      socket.emit('connected', {
        message: 'Connected to real-time updates',
        timestamp: new Date().toISOString()
      });
    });

    logger.info('WebSocket service initialized');
  }

  // Emit inventory updates
  emitInventoryUpdate(data) {
    if (this.io) {
      this.io.to('inventory-updates').emit('inventory:updated', {
        type: 'inventory_update',
        data,
        timestamp: new Date().toISOString()
      });
      logger.info('Inventory update emitted to subscribers');
    }
  }

  // Emit low stock alerts
  emitLowStockAlert(data) {
    if (this.io) {
      this.io.to('admin').emit('inventory:low-stock-alert', {
        type: 'low_stock_alert',
        data,
        timestamp: new Date().toISOString()
      });
      logger.info('Low stock alert emitted to admin users');
    }
  }

  // Emit order updates
  emitOrderUpdate(data) {
    if (this.io) {
      this.io.to('order-updates').emit('order:updated', {
        type: 'order_update',
        data,
        timestamp: new Date().toISOString()
      });
      logger.info('Order update emitted to subscribers');
    }
  }

  // Emit new order notifications
  emitNewOrder(data) {
    if (this.io) {
      this.io.to('admin').emit('order:new', {
        type: 'new_order',
        data,
        timestamp: new Date().toISOString()
      });
      logger.info('New order notification emitted to admin users');
    }
  }

  // Emit dashboard data updates
  emitDashboardUpdate(data) {
    if (this.io) {
      this.io.to('dashboard-updates').emit('dashboard:updated', {
        type: 'dashboard_update',
        data,
        timestamp: new Date().toISOString()
      });
      logger.info('Dashboard update emitted to subscribers');
    }
  }

  // Emit user activity updates
  emitUserActivity(data) {
    if (this.io) {
      this.io.to('admin').emit('user:activity', {
        type: 'user_activity',
        data,
        timestamp: new Date().toISOString()
      });
      logger.info('User activity update emitted to admin users');
    }
  }

  // Get connected users count
  getConnectedUsersCount() {
    return this.connectedUsers.size;
  }

  // Get connected admin users
  getConnectedAdminUsers() {
    const adminUsers = [];
    this.connectedUsers.forEach((user, userId) => {
      if (user.role === 'Admin' || user.role === 'Employee') {
        adminUsers.push({
          userId,
          role: user.role,
          connectedAt: user.connectedAt
        });
      }
    });
    return adminUsers;
  }

  // Send notification to specific user
  sendNotificationToUser(userId, notification) {
    const user = this.connectedUsers.get(userId);
    if (user && this.io) {
      this.io.to(user.socketId).emit('notification', {
        type: 'notification',
        data: notification,
        timestamp: new Date().toISOString()
      });
      logger.info(`Notification sent to user ${userId}`);
    }
  }

  // Broadcast system announcement
  broadcastAnnouncement(announcement) {
    if (this.io) {
      this.io.emit('system:announcement', {
        type: 'system_announcement',
        data: announcement,
        timestamp: new Date().toISOString()
      });
      logger.info('System announcement broadcasted to all users');
    }
  }

  // Product-specific event emitters
  emitProductCreated(productData) {
    if (this.io) {
      this.io.to('product-updates').emit('productCreated', {
        type: 'product_created',
        data: productData,
        timestamp: new Date().toISOString()
      });
      logger.info('Product created event emitted to subscribers');
    }
  }

  emitProductUpdated(productData) {
    if (this.io) {
      this.io.to('product-updates').emit('productUpdated', {
        type: 'product_updated',
        data: productData,
        timestamp: new Date().toISOString()
      });
      logger.info('Product updated event emitted to subscribers');
    }
  }

  emitProductDeleted(productId) {
    if (this.io) {
      this.io.to('product-updates').emit('productDeleted', {
        type: 'product_deleted',
        data: { productId },
        timestamp: new Date().toISOString()
      });
      logger.info('Product deleted event emitted to subscribers');
    }
  }

  emitProductFileUploaded(productId, fileData) {
    if (this.io) {
      this.io.to('product-updates').emit('productFileUploaded', {
        type: 'product_file_uploaded',
        data: { productId, fileData },
        timestamp: new Date().toISOString()
      });
      logger.info('Product file uploaded event emitted to subscribers');
    }
  }

  emitProductStatusChanged(productId, status) {
    if (this.io) {
      this.io.to('product-updates').emit('productStatusChanged', {
        type: 'product_status_changed',
        data: { productId, status },
        timestamp: new Date().toISOString()
      });
      logger.info('Product status changed event emitted to subscribers');
    }
  }
}

// Create singleton instance
const websocketService = new WebSocketService();

module.exports = websocketService;
