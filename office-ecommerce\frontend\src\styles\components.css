/* Header Component Styles */
.header {
    background: var(--white);
    position: sticky;
    top: 0;
    z-index: 1000;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
}

/* Special Offer Banner */
.special-offer-banner {
    background: url("data:image/svg+xml,%3Csvg width='100%25' height='100%25' xmlns='http://www.w3.org/2000/svg'%3E%3Cdefs%3E%3ClinearGradient id='gradient' x1='0%25' y1='0%25' x2='100%25' y2='0%25'%3E%3Cstop offset='0%25' style='stop-color:%23F0B21B;stop-opacity:1' /%3E%3Cstop offset='50%25' style='stop-color:%23e6a632;stop-opacity:1' /%3E%3Cstop offset='100%25' style='stop-color:%23F0B21B;stop-opacity:1' /%3E%3C/linearGradient%3E%3C/defs%3E%3Crect width='100%25' height='100%25' fill='url(%23gradient)' /%3E%3C/svg%3E") #F0B21B;
    color: var(--white);
    padding: 0.5rem 0;
    font-size: 0.9rem;
    position: relative;
    overflow: hidden;
}

.offer-content {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 0.5rem;
    position: relative;
}

.offer-icon {
    font-size: 1.2rem;
}

.offer-text {
    font-weight: bold;
    color: var(--white);
    text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.3);
}

.offer-details {
    color: var(--white);
    text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.2);
}

.offer-shop-btn {
    background: var(--white);
    color: #F0B21B;
    border: 2px solid var(--white);
    padding: 0.25rem 0.75rem;
    border-radius: 4px;
    font-size: 0.8rem;
    font-weight: 600;
    cursor: pointer;
    margin-left: 1rem;
    transition: all 0.3s ease;
}

.offer-shop-btn:hover {
    background: transparent;
    color: var(--white);
    border-color: var(--white);
}

.offer-close {
    position: absolute;
    right: 0;
    background: none;
    border: none;
    color: var(--white);
    font-size: 1.5rem;
    cursor: pointer;
    padding: 0 0.5rem;
    transition: all 0.3s ease;
    text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.3);
}

.offer-close:hover {
    color: rgba(255, 255, 255, 0.8);
    transform: scale(1.1);
}

/* Top Header */
.top-header {
    padding: 0.5rem 0;
    font-size: 0.85rem;
    color: #6c757d;
}

.top-header-content {
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.contact-info {
    display: flex;
    gap: 2rem;
}

.contact-item {
    display: flex;
    align-items: center;
    gap: 0.25rem;
}

.location-info {
    font-size: 0.8rem;
}

/* Main Header - Base styles (positioning handled by unified container) */
.main-header {
    background: var(--white);
    padding: 1rem 0;
    border-bottom: 1px solid #e9ecef;
    margin: 0; /* Ensure no extra spacing */
}

.main-header-content {
    display: grid;
    grid-template-columns: 1fr 2fr 1fr;
    align-items: center;
    gap: 2rem;
}

/* Header Search - Simple Layout */
.header-search {
    display: flex;
    align-items: center;
    max-width: 350px;
    width: 100%;
}

.header-search-input {
    width: 100%;
}

.header-search .search-input-wrapper {
    background: #f8f9fa;
    border: 1px solid #e9ecef;
    border-radius: 6px;
    padding: 0;
    overflow: hidden;
    transition: border-color 0.2s ease;
}

.header-search .search-input-wrapper:focus-within {
    border-color: #F0B21B;
}

.header-search .search-input {
    padding: 0.6rem 1rem;
    font-size: 0.9rem;
    background: transparent;
    border: none;
    outline: none;
}

.header-search .search-input::placeholder {
    color: #6c757d;
}

.header-search .search-button {
    padding: 0.75rem 1.25rem;
    background: none;
    border: none;
    color: #F0B21B;
    cursor: pointer;
    transition: all 0.3s ease;
}

.header-search .search-button:hover {
    color: #d49a16;
    transform: scale(1.05);
}

.header-search .search-input-wrapper:focus-within {
    border-color: #F0B21B;
    box-shadow: 0 0 0 2px rgba(240, 178, 27, 0.2);
}

/* Header Logo */
.header-logo {
    display: flex;
    flex-direction: column;
    align-items: center;
    text-align: center;
    flex: 1;
    max-width: 300px;
    margin: 0 auto;
}

.logo {
    display: flex;
    flex-direction: column;
    text-decoration: none;
    color: var(--text-dark);
    transition: transform 0.3s ease;
    width: 100%;
    max-width: 240px;
}

.logo:hover {
    transform: scale(1.02);
}

.logo-tagline {
    font-size: 0.75rem;
    color: #6c757d;
    margin-top: 0.5rem;
    letter-spacing: 1px;
    font-weight: 500;
    white-space: nowrap;
}

/* Header Actions */
.header-actions {
    display: flex;
    align-items: center;
    justify-content: flex-end;
    gap: 1rem;
}

/* Enhanced Header Actions Layout */
.header-actions .user-menu {
    margin-left: 0.5rem;
    padding-left: 0.5rem;
    border-left: 1px solid rgba(240, 178, 27, 0.2);
}

.header-actions .action-btn.user-btn {
    position: relative;
}

.header-actions .action-btn.user-btn::after {
    content: '';
    position: absolute;
    right: -0.75rem;
    top: 50%;
    transform: translateY(-50%);
    width: 1px;
    height: 20px;
    background: rgba(240, 178, 27, 0.2);
    display: none;
}

/* Authentication Section Styling */
.header-actions .user-menu .logout-btn {
    margin-left: 0.25rem;
}

.header-actions .action-btn.user-btn:hover {
    transform: translateY(-1px);
    transition: all 0.2s ease;
}

.header-actions .contact-btn {
    position: relative;
}

.header-actions .contact-btn::after {
    content: '';
    position: absolute;
    right: -0.5rem;
    top: 50%;
    transform: translateY(-50%);
    width: 1px;
    height: 20px;
    background: rgba(240, 178, 27, 0.2);
}

.action-btn {
    background: none;
    border: none;
    font-size: 1.5rem;
    cursor: pointer;
    padding: 0.5rem;
    border-radius: 50%;
    transition: background-color 0.3s;
    text-decoration: none;
    color: inherit;
    display: flex;
    align-items: center;
    justify-content: center;
}

.action-btn:hover {
    background: #f8f9fa;
}

.action-btn svg {
    transition: all 0.3s ease;
}

.action-btn:hover svg path,
.action-btn:hover svg polyline {
    stroke: #e6a632;
}

/* ===== UNIFIED HEADER CONTAINER - COORDINATED SCROLL BEHAVIOR ===== */

/* Unified Header Container - Wraps Main Header + Navigation */
.unified-header-container {
    position: sticky;
    top: 0;
    z-index: 999;
    transition: transform 0.3s cubic-bezier(0.4, 0, 0.2, 1),
                box-shadow 0.3s ease,
                background-color 0.3s ease;
    transform: translateY(0);
    will-change: transform;
    backface-visibility: hidden;
    -webkit-backface-visibility: hidden;
    /* Ensure no spacing between sections */
    margin: 0;
    padding: 0;
}

/* Unified scroll-based states */
.unified-header-container.unified-hidden {
    transform: translateY(-100%);
}

.unified-header-container.unified-floating {
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
}

/* Base styles for elements within unified container */
.unified-header-container .main-header {
    background: var(--white);
    padding: 1rem 0;
    border-bottom: 1px solid #e9ecef;
    transition: all 0.3s ease;
    /* Ensure no extra spacing */
    margin: 0;
}

.unified-header-container .navigation-bar {
    background: #F0B21B;
    padding: 0;
    margin: 0;
    transition: all 0.3s ease;
    /* Ensure seamless connection with main header */
    border-top: none;
    /* Remove any potential spacing */
    margin-top: 0;
}

/* Enhanced floating state styles - Remove border and ensure seamless appearance */
.unified-header-container.unified-floating .main-header {
    border-bottom: none; /* Remove border for seamless floating appearance */
    /* Ensure no background bleeding or spacing */
    background: var(--white);
    margin-bottom: 0;
    padding-bottom: 1rem;
}

.unified-header-container.unified-floating .nav-link {
    color: var(--white);
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
}

.main-navigation {
    display: flex;
    justify-content: center;
    align-items: center;
}

.nav-link {
    color: var(--white);
    text-decoration: none;
    padding: 1rem 1.5rem;
    font-weight: 500;
    font-size: 0.9rem;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    border-bottom: 3px solid transparent;
    position: relative;
    display: flex;
    align-items: center;
}

.nav-link:hover,
.nav-link.active {
    background: rgba(255, 255, 255, 0.1);
    border-bottom-color: var(--white);
}

/* Design Excellence Logo Styles */
.logo-container {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 100%;
    height: auto;
    overflow: visible;
    padding: 0.5rem;
}

.design-excellence-logo {
    transition: all 0.3s ease;
    width: 100%;
    height: auto;
    max-width: 240px;
    max-height: 80px;
    object-fit: contain;
    display: block;
    overflow: visible;
}

.design-excellence-logo:hover {
    transform: scale(1.05);
}

.design-excellence-logo .dc-letters {
    filter: drop-shadow(0 2px 4px rgba(0, 0, 0, 0.1));
}

.design-excellence-logo .main-text {
    filter: drop-shadow(0 1px 2px rgba(0, 0, 0, 0.1));
}

.design-excellence-logo .main-text text {
    font-family: 'Arial', 'Helvetica', sans-serif;
    font-weight: bold;
    letter-spacing: 0.5px;
}

/* Ensure logo is always visible and properly sized */
.design-excellence-logo {
    min-width: 120px;
    min-height: 40px;
}

/* Logo container improvements */
.logo-container {
    min-height: 60px;
    display: flex;
    align-items: center;
    justify-content: center;
}

/* User Menu Styles */
.user-menu {
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.user-greeting {
    color: var(--text-dark);
    font-size: 0.8rem;
    display: none;
}

.login-btn,
.logout-btn {
    background: var(--primary-color);
    color: white;
    padding: 0.25rem 0.75rem;
    border: none;
    border-radius: 4px;
    text-decoration: none;
    cursor: pointer;
    transition: background-color 0.3s ease;
    font-size: 0.8rem;
}

.login-btn:hover,
.logout-btn:hover {
    background: var(--primary-dark);
}

/* Modern Logout Button Design */
.logout-btn {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    background: linear-gradient(135deg, #e74c3c 0%, #c0392b 100%);
    color: #FFDB58;
    padding: 0.5rem 1rem;
    border: none;
    border-radius: 8px;
    font-size: 0.875rem;
    font-weight: 600;
    font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', sans-serif;
    cursor: pointer;
    transition: all 0.3s ease;
    box-shadow: 0 2px 4px rgba(231, 76, 60, 0.2);
    text-transform: none;
    letter-spacing: 0.025em;
}

.logout-btn:hover {
    background: linear-gradient(135deg, #c0392b 0%, #a93226 100%);
    box-shadow: 0 4px 12px rgba(231, 76, 60, 0.3);
    transform: translateY(-1px);
}

.logout-btn:active {
    transform: translateY(0);
    box-shadow: 0 2px 4px rgba(231, 76, 60, 0.2);
}

.logout-btn svg {
    width: 16px;
    height: 16px;
    stroke-width: 2;
}

.logout-btn span {
    font-weight: 600;
}

/* Logout Button Focus and Accessibility */
.logout-btn:focus {
    outline: none;
    box-shadow: 0 0 0 3px rgba(231, 76, 60, 0.3);
}

.logout-btn:focus-visible {
    outline: 2px solid #e74c3c;
    outline-offset: 2px;
}

/* Logout Button Loading State (optional for future use) */
.logout-btn.loading {
    opacity: 0.7;
    cursor: not-allowed;
    pointer-events: none;
}

.logout-btn.loading svg {
    animation: spin 1s linear infinite;
}

@keyframes spin {
    from {
        transform: rotate(0deg);
    }
    to {
        transform: rotate(360deg);
    }
}

.mobile-menu-toggle {
    display: none;
    flex-direction: column;
    background: none;
    border: none;
    cursor: pointer;
    padding: 0.5rem;
}

.mobile-menu-toggle span {
    width: 25px;
    height: 3px;
    background: var(--text-dark);
    margin: 3px 0;
    transition: 0.3s;
}

.mobile-nav {
    display: none;
    flex-direction: column;
    background: var(--white);
    border-top: 1px solid var(--border-color);
    padding: 1rem 0;
}

.mobile-nav.open {
    display: flex;
}

.mobile-nav-link {
    padding: 0.75rem 0;
    text-decoration: none;
    color: var(--text-dark);
    border-bottom: 1px solid var(--border-color);
}

.mobile-nav-link:hover {
    color: var(--primary-color);
}

.mobile-nav-link.login {
    background: var(--primary-color);
    color: white;
    text-align: center;
    margin-top: 1rem;
    border-radius: 4px;
}

/* Header Responsive Styles */
@media (max-width: 768px) {
    .special-offer-banner {
        font-size: 0.8rem;
        padding: 0.25rem 0;
    }

    .offer-details {
        display: none;
    }

    .top-header-content {
        flex-direction: column;
        gap: 0.5rem;
        text-align: center;
    }

    .contact-info {
        gap: 1rem;
    }

    .currency-language-selector {
        order: -1;
        justify-content: center;
        gap: 0.5rem;
        font-size: 0.8rem;
    }

    .currency-language-selector .select-trigger {
        padding: 0.4rem 0.6rem;
        min-width: 70px;
        font-size: 0.8rem;
    }

    .currency-language-selector .select-dropdown {
        min-width: 180px;
    }

    .currency-language-selector .select-option {
        padding: 0.75rem 0.875rem;
        font-size: 0.85rem;
    }

    /* Search Mobile Styles - Simple Layout */
    .search-container {
        max-width: 100%;
    }

    .search-input-wrapper {
        border-radius: 6px;
    }

    .search-input {
        padding: 0.75rem 1rem;
        font-size: 0.9rem;
    }

    .search-button {
        padding: 0.75rem 1rem;
    }

    .search-suggestions {
        max-height: 350px;
        border-radius: 6px;
    }

    .suggestion-item {
        padding: 0.75rem 1rem;
        gap: 0.75rem;
    }

    .suggestion-image {
        width: 45px;
        height: 45px;
    }

    .suggestion-name {
        font-size: 0.85rem;
    }

    .suggestion-category {
        font-size: 0.7rem;
    }

    .suggestion-pricing {
        gap: 0.25rem;
    }

    .suggestion-price.current {
        font-size: 0.85rem;
    }

    .suggestions-header {
        padding: 0.75rem 1rem;
    }

    .keyboard-hint {
        display: none;
    }

    .main-header-content {
        grid-template-columns: 1fr;
        gap: 1rem;
        text-align: center;
    }

    .header-search {
        order: 3;
        max-width: 100%;
    }

    .header-logo {
        order: 1;
        max-width: 200px;
    }

    .header-actions {
        order: 2;
        justify-content: center;
    }

    /* Mobile Header Actions Repositioning */
    .header-actions .user-menu {
        margin-left: 0.25rem;
        padding-left: 0.25rem;
        border-left: 1px solid rgba(240, 178, 27, 0.15);
    }

    .header-actions .action-btn.user-btn::after {
        display: none;
    }

    .header-actions .contact-btn::after {
        right: -0.25rem;
        height: 16px;
    }

    /* Mobile logo adjustments */
    .design-excellence-logo {
        max-width: 180px;
        max-height: 60px;
    }

    .logo-tagline {
        font-size: 0.65rem;
        margin-top: 0.25rem;
    }

    .main-navigation {
        flex-wrap: wrap;
        justify-content: center;
    }

    .nav-link {
        padding: 0.75rem 1rem;
        font-size: 0.8rem;
    }

    .mobile-menu-toggle {
        display: flex;
    }

    .user-greeting {
        display: none;
    }

    /* Mobile Logout Button */
    .logout-btn {
        padding: 0.375rem 0.75rem;
        font-size: 0.8rem;
        gap: 0.375rem;
    }

    .logout-btn span {
        display: none;
    }

    .logout-btn svg {
        width: 18px;
        height: 18px;
    }
}

@media (max-width: 480px) {
    .contact-info {
        flex-direction: column;
        gap: 0.25rem;
    }

    .main-navigation {
        flex-direction: column;
    }

    .nav-link {
        width: 100%;
        text-align: center;
        border-bottom: 1px solid rgba(255, 255, 255, 0.1);
    }

    .header-actions {
        gap: 0.5rem;
    }

    /* Extra Small Mobile Header Actions */
    .header-actions .user-menu {
        margin-left: 0.125rem;
        padding-left: 0.125rem;
        border-left: none;
    }

    .header-actions .contact-btn::after {
        display: none;
    }

    .action-btn {
        font-size: 1.2rem;
        padding: 0.25rem;
    }

    /* Extra small mobile logo adjustments */
    .header-logo {
        max-width: 160px;
    }

    .design-excellence-logo {
        max-width: 150px;
        max-height: 50px;
    }

    .logo-tagline {
        font-size: 0.6rem;
        letter-spacing: 0.5px;
    }
}

/* Product Card Component */
.product-card {
    background: var(--white);
    border-radius: 12px;
    overflow: hidden;
    box-shadow: var(--shadow);
    transition: transform 0.3s, box-shadow 0.3s;
    position: relative;
    max-width: 320px;
    margin: 0 auto;
}

.product-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 8px 25px rgba(0,0,0,0.15);
}

.featured-badge,
.discount-badge {
    position: absolute;
    top: 10px;
    left: 10px;
    background: var(--accent-color);
    color: white;
    padding: 4px 8px;
    border-radius: 12px;
    font-size: 0.75rem;
    font-weight: bold;
    z-index: 2;
}

.discount-badge {
    background: var(--primary-color);
    top: 10px;
    right: 10px;
    left: auto;
}

.product-link {
    text-decoration: none;
    color: inherit;
}

.product-image {
    position: relative;
    height: 250px;
    overflow: hidden;
}

.product-image img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: transform 0.3s;
}

.product-card:hover .product-image img {
    transform: scale(1.05);
}



.product-info {
    padding: 1.2rem;
}

/* Product Actions */
.product-actions {
    display: flex;
    gap: 8px;
    padding: 0 1.2rem 1rem;
}

.btn-compact {
    flex: 1;
    padding: 0.6rem 1rem;
    font-size: 0.85rem;
    font-weight: 600;
    border-radius: 6px;
    text-align: center;
    transition: all 0.3s ease;
}

/* 3D Configuration Actions */
.config-actions {
    display: flex;
    justify-content: center;
    gap: 8px;
    padding: 0 1.2rem 1.2rem;
    border-top: 1px solid var(--border-color);
    margin-top: 0.5rem;
    padding-top: 1rem;
}

.config-btn {
    width: 36px;
    height: 36px;
    border: none;
    border-radius: 8px;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: all 0.3s ease;
    text-decoration: none;
    position: relative;
    overflow: hidden;
}

.config-btn::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.3), transparent);
    transition: left 0.4s ease;
}

.config-btn:hover::before {
    left: 100%;
}

.config-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

/* Individual config button styles */
.color-btn {
    background: linear-gradient(135deg, #FF6B6B, #FF8E8E);
    color: white;
}

.color-btn:hover {
    background: linear-gradient(135deg, #FF5252, #FF7979);
}

.color-btn.active {
    background: linear-gradient(135deg, #FF5252, #FF7979);
    box-shadow: 0 0 0 2px rgba(255, 107, 107, 0.3);
    transform: scale(1.05);
}

.material-btn {
    background: linear-gradient(135deg, #4ECDC4, #6BCCC4);
    color: white;
}

.material-btn:hover {
    background: linear-gradient(135deg, #26D0CE, #4ECDC4);
}

.material-btn.active {
    background: linear-gradient(135deg, #26D0CE, #4ECDC4);
    box-shadow: 0 0 0 2px rgba(78, 205, 196, 0.3);
    transform: scale(1.05);
}

.size-btn {
    background: linear-gradient(135deg, #45B7D1, #6BB6D6);
    color: white;
}

.size-btn:hover {
    background: linear-gradient(135deg, #2196F3, #45B7D1);
}

.size-btn.active {
    background: linear-gradient(135deg, #2196F3, #45B7D1);
    box-shadow: 0 0 0 2px rgba(69, 183, 209, 0.3);
    transform: scale(1.05);
}

.full-config-btn {
    background: linear-gradient(135deg, #F0B21B, #e6a632);
    color: white;
    box-shadow: 0 3px 10px rgba(240, 178, 27, 0.3);
}

.full-config-btn:hover {
    background: linear-gradient(135deg, #e6a632, #d4941a);
    box-shadow: 0 5px 15px rgba(240, 178, 27, 0.4);
}

/* Tooltip styles for config buttons */
.config-btn[title]:hover::after {
    content: attr(title);
    position: absolute;
    bottom: -35px;
    left: 50%;
    transform: translateX(-50%);
    background: rgba(0, 0, 0, 0.8);
    color: white;
    padding: 4px 8px;
    border-radius: 4px;
    font-size: 0.75rem;
    white-space: nowrap;
    z-index: 10;
    pointer-events: none;
}

/* Responsive adjustments for product cards */
@media (max-width: 768px) {
    .product-card {
        max-width: 280px;
    }

    .product-info {
        padding: 1rem;
    }

    .product-actions {
        padding: 0 1rem 0.8rem;
        gap: 6px;
    }

    .config-actions {
        padding: 0 1rem 1rem;
        gap: 6px;
    }

    .config-btn {
        width: 32px;
        height: 32px;
    }

    .btn-compact {
        padding: 0.5rem 0.8rem;
        font-size: 0.8rem;
    }
}

@media (max-width: 480px) {
    .product-card {
        max-width: 100%;
        margin: 0;
    }

    .config-actions {
        gap: 4px;
    }

    .config-btn {
        width: 30px;
        height: 30px;
    }

    .config-btn svg {
        width: 14px;
        height: 14px;
    }
}

.product-category {
    color: var(--text-light);
    font-size: 0.85rem;
    margin-bottom: 0.5rem;
}

.product-name {
    font-size: 1.1rem;
    font-weight: 600;
    margin-bottom: 1rem;
    color: var(--text-dark);
}

.product-pricing {
    display: flex;
    flex-direction: column;
    gap: 0.25rem;
    margin-bottom: 1rem;
}

.current-price {
    font-size: 1.25rem;
    font-weight: bold;
    color: var(--primary-color);
}

.base-price {
    font-size: 0.9rem;
    color: var(--text-light);
    text-decoration: line-through;
}

.original-price {
    font-size: 1rem;
    color: var(--text-light);
    text-decoration: line-through;
}

.config-indicator {
    font-size: 0.75rem;
    color: var(--accent-color);
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.product-actions {
    display: flex;
    gap: 0.5rem;
}

.product-actions .btn {
    flex: 1;
    text-align: center;
    padding: 0.75rem;
    font-size: 0.9rem;
}

/* Product Filter Component */
.product-filter {
    background: var(--white);
    border-radius: 12px;
    padding: 2rem;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
    margin-bottom: 2rem;
    border: 1px solid #f0f0f0;
}

.filter-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 2rem;
    padding-bottom: 1rem;
    border-bottom: 2px solid #f8f9fa;
}

.filter-header h3 {
    margin: 0;
    color: var(--text-dark);
    font-size: 1.4rem;
    font-weight: 600;
    letter-spacing: -0.02em;
}

.mobile-filter-toggle {
    display: none;
    background: #F0B21B;
    color: white;
    border: none;
    padding: 0.75rem 1.5rem;
    border-radius: 8px;
    cursor: pointer;
    font-weight: 500;
    font-size: 0.9rem;
    transition: all 0.3s ease;
    box-shadow: 0 2px 8px rgba(240, 178, 27, 0.3);
}

.mobile-filter-toggle:hover {
    background: #d49a16;
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(240, 178, 27, 0.4);
}

.filter-content {
    display: block;
}

.filter-content.open {
    display: block;
}

.filter-section {
    margin-bottom: 2rem;
    padding-bottom: 1.5rem;
    border-bottom: 1px solid #f0f0f0;
}

.filter-section:last-child {
    border-bottom: none;
    margin-bottom: 0;
    padding-bottom: 0;
}

.filter-section label {
    display: block;
    margin-bottom: 0.75rem;
    font-weight: 600;
    color: var(--text-dark);
    font-size: 1rem;
    letter-spacing: -0.01em;
}

.filter-section input,
.filter-section select {
    width: 100%;
    padding: 1rem;
    border: 2px solid #e5e7eb;
    border-radius: 8px;
    font-size: 0.95rem;
    font-weight: 400;
    transition: all 0.3s ease;
    background: #fafafa;
    color: var(--text-dark);
}

.filter-section input:focus,
.filter-section select:focus {
    outline: none;
    border-color: #F0B21B;
    background: var(--white);
    box-shadow: 0 0 0 3px rgba(240, 178, 27, 0.1);
    transform: translateY(-1px);
}

.filter-section input:hover,
.filter-section select:hover {
    border-color: #d1d5db;
    background: var(--white);
}

.filter-section select {
    cursor: pointer;
    appearance: none;
    background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 20 20'%3e%3cpath stroke='%236b7280' stroke-linecap='round' stroke-linejoin='round' stroke-width='1.5' d='m6 8 4 4 4-4'/%3e%3c/svg%3e");
    background-position: right 0.75rem center;
    background-repeat: no-repeat;
    background-size: 1.5em 1.5em;
    padding-right: 3rem;
}

.price-inputs {
    display: flex;
    align-items: center;
    gap: 1rem;
}

.price-inputs input {
    flex: 1;
}

.price-inputs span {
    color: var(--text-light);
    font-size: 0.9rem;
    font-weight: 500;
    padding: 0 0.5rem;
}

.checkbox-label {
    display: flex;
    align-items: center;
    gap: 0.75rem;
    cursor: pointer;
    padding: 0.75rem;
    border-radius: 8px;
    transition: all 0.3s ease;
    font-weight: 500;
}

.checkbox-label:hover {
    background: #f8f9fa;
}

.checkbox-label input[type="checkbox"] {
    width: 1.25rem;
    height: 1.25rem;
    accent-color: #F0B21B;
    cursor: pointer;
}

.filter-actions {
    margin-top: 2rem;
    padding-top: 1.5rem;
    border-top: 2px solid #f8f9fa;
}

.filter-actions .btn {
    width: 100%;
    padding: 1rem;
    font-weight: 600;
    font-size: 0.95rem;
    border-radius: 8px;
    transition: all 0.3s ease;
}

.filter-actions .btn-secondary {
    background: #f8f9fa;
    color: var(--text-dark);
    border: 2px solid #e5e7eb;
}

.filter-actions .btn-secondary:hover {
    background: #F0B21B;
    color: var(--white);
    border-color: #F0B21B;
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(240, 178, 27, 0.3);
}



.model-loaded-notice {
    display: flex;
    align-items: flex-start;
    gap: 0.75rem;
    padding: 1rem;
    background: #d4edda;
    color: #155724;
    border: 1px solid #c3e6cb;
    border-radius: 8px;
    font-size: 0.9rem;
    line-height: 1.4;
    margin-bottom: 1rem;
}

.model-error-notice {
    display: flex;
    align-items: flex-start;
    gap: 0.75rem;
    padding: 1rem;
    background: #fff3cd;
    color: #856404;
    border: 1px solid #ffeaa7;
    border-radius: 8px;
    font-size: 0.9rem;
    line-height: 1.4;
    margin-bottom: 1rem;
}



/* Currency Language Selector Styles */
.currency-language-selector {
    display: flex;
    align-items: center;
    gap: 0.75rem;
    font-size: 0.85rem;
    color: var(--text-light);
}

.selector-item {
    position: relative;
}

.custom-select {
    position: relative;
    cursor: pointer;
}

.select-trigger {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    padding: 0.5rem 0.75rem;
    background: rgba(255, 255, 255, 0.1);
    border: 1px solid rgba(255, 255, 255, 0.2);
    border-radius: 6px;
    color: var(--white);
    font-size: 0.85rem;
    transition: all 0.3s ease;
    min-width: 80px;
    justify-content: space-between;
}

.custom-select:hover .select-trigger {
    background: rgba(255, 255, 255, 0.15);
    border-color: rgba(255, 255, 255, 0.3);
}

.custom-select.open .select-trigger {
    background: rgba(255, 255, 255, 0.2);
    border-color: #F0B21B;
}

.select-value {
    font-weight: 500;
    white-space: nowrap;
}

.select-arrow {
    transition: transform 0.3s ease;
    flex-shrink: 0;
}

.select-arrow.rotated {
    transform: rotate(180deg);
}

.select-dropdown {
    position: absolute;
    top: calc(100% + 0.5rem);
    left: 0;
    right: 0;
    background: var(--white);
    border: 1px solid var(--border-color);
    border-radius: 8px;
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
    z-index: 1000;
    overflow: hidden;
    animation: fadeInDown 0.3s ease;
    min-width: 200px;
}

.select-option {
    display: flex;
    align-items: center;
    gap: 0.75rem;
    padding: 0.875rem 1rem;
    color: var(--text-dark);
    font-size: 0.9rem;
    cursor: pointer;
    transition: all 0.2s ease;
    border-bottom: 1px solid #f0f0f0;
}

.select-option:last-child {
    border-bottom: none;
}

.select-option:hover {
    background: #f8f9fa;
    color: var(--text-dark);
}

.select-option.selected {
    background: #F0B21B;
    color: var(--white);
}

.select-option.selected:hover {
    background: #d49a16;
}

.option-symbol {
    font-weight: 600;
    font-size: 1rem;
    min-width: 20px;
}

.option-code {
    font-weight: 600;
    min-width: 40px;
}

.option-name {
    font-weight: 500;
    color: var(--text-light);
    flex: 1;
}

.select-option.selected .option-name {
    color: rgba(255, 255, 255, 0.9);
}

.selector-divider {
    color: rgba(255, 255, 255, 0.4);
    font-weight: 300;
    margin: 0 0.25rem;
}

/* Animation for dropdown */
@keyframes fadeInDown {
    from {
        opacity: 0;
        transform: translateY(-10px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* Search Component Styles - Simple Layout */
.search-container {
    position: relative;
    width: 100%;
    max-width: 400px;
}

.search-form {
    width: 100%;
}

.search-input-wrapper {
    position: relative;
    display: flex;
    align-items: center;
    background: #f8f9fa;
    border: 1px solid #e9ecef;
    border-radius: 6px;
    transition: border-color 0.2s ease;
}

.search-input-wrapper:focus-within {
    border-color: #F0B21B;
}

.search-input {
    flex: 1;
    padding: 0.75rem 1rem;
    border: none;
    outline: none;
    font-size: 0.9rem;
    background: transparent;
    color: var(--text-dark);
}

.search-input::placeholder {
    color: #6c757d;
}

.search-button {
    padding: 1rem 1.25rem;
    background: none;
    border: none;
    color: #F0B21B;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    justify-content: center;
}

.search-button:hover {
    color: #d49a16;
    transform: scale(1.05);
}

.search-button:disabled {
    color: var(--text-light);
    cursor: not-allowed;
    transform: none;
}

.search-loading {
    position: absolute;
    right: 50px;
    top: 50%;
    transform: translateY(-50%);
    display: flex;
    align-items: center;
    justify-content: center;
}

.loading-spinner {
    width: 16px;
    height: 16px;
    border: 2px solid #e9ecef;
    border-top: 2px solid #F0B21B;
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* Search Suggestions - Simple Layout */
.search-suggestions {
    position: absolute;
    top: calc(100% + 0.25rem);
    left: 0;
    right: 0;
    background: var(--white);
    border: 1px solid #e9ecef;
    border-radius: 6px;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
    z-index: 1000;
    overflow: hidden;
    max-height: 400px;
    overflow-y: auto;
}

.suggestions-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0.75rem 1rem;
    background: #f8f9fa;
    border-bottom: 1px solid #e9ecef;
}

.suggestions-count {
    font-size: 0.8rem;
    color: #6c757d;
    font-weight: 400;
}

.suggestions-close {
    background: none;
    border: none;
    color: #6c757d;
    cursor: pointer;
    padding: 0.25rem;
    border-radius: 3px;
    transition: color 0.2s ease;
}

.suggestions-close:hover {
    color: var(--text-dark);
}

.suggestions-list {
    max-height: 300px;
    overflow-y: auto;
}

.suggestion-item {
    display: flex;
    align-items: center;
    gap: 0.75rem;
    padding: 0.75rem 1rem;
    cursor: pointer;
    transition: background-color 0.2s ease;
    border-bottom: 1px solid #f8f9fa;
}

.suggestion-item:last-child {
    border-bottom: none;
}

.suggestion-item:hover,
.suggestion-item.selected {
    background: #f8f9fa;
}

.suggestion-image {
    width: 50px;
    height: 50px;
    border-radius: 4px;
    overflow: hidden;
    flex-shrink: 0;
    background: #f8f9fa;
}

.suggestion-image img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.suggestion-content {
    flex: 1;
    min-width: 0;
}

.suggestion-name {
    font-size: 0.9rem;
    font-weight: 500;
    color: var(--text-dark);
    margin: 0 0 0.25rem 0;
    line-height: 1.3;
}

.suggestion-category {
    font-size: 0.75rem;
    color: #6c757d;
    margin-bottom: 0.25rem;
}

.suggestion-pricing {
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.suggestion-price {
    font-weight: 500;
}

.suggestion-price.current {
    color: #F0B21B;
    font-size: 0.9rem;
}

.suggestion-price.original {
    color: #6c757d;
    text-decoration: line-through;
    font-size: 0.8rem;
}

/* No suggestions state */
.no-suggestions {
    padding: 2rem 1rem;
    text-align: center;
    color: #6c757d;
    font-size: 0.9rem;
}

.suggestion-badge {
    display: flex;
    align-items: center;
    gap: 0.25rem;
    font-size: 0.75rem;
    color: #F0B21B;
    font-weight: 500;
}

.suggestion-arrow {
    color: var(--text-light);
    transition: all 0.3s ease;
}

.suggestion-item:hover .suggestion-arrow,
.suggestion-item.selected .suggestion-arrow {
    color: #F0B21B;
    transform: translateX(4px);
}

.suggestions-footer {
    padding: 0.75rem 1.25rem;
    background: #f8f9fa;
    border-top: 1px solid #e5e7eb;
}

.keyboard-hint {
    font-size: 0.75rem;
    color: var(--text-light);
    text-align: center;
}

.suggestions-loading {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 0.75rem;
    padding: 2rem;
    color: var(--text-light);
}

.no-suggestions {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    gap: 1rem;
    padding: 2rem;
    color: var(--text-light);
    text-align: center;
}

.no-suggestions svg {
    opacity: 0.5;
}

/* Table Configurator Styles */
.table-configurator {
    min-height: 100vh;
    background: #f8f9fa;
}

.breadcrumb-nav {
    background: white;
    padding: 1rem;
    border-bottom: 1px solid #e9ecef;
    font-size: 0.9rem;
    color: #6c757d;
}

.nav-content {
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.breadcrumb-links a {
    color: var(--primary-color);
    text-decoration: none;
}

.back-btn {
    background: var(--primary-color);
    color: white;
    border: none;
    padding: 0.5rem 1rem;
    border-radius: 0.25rem;
    cursor: pointer;
    font-size: 0.9rem;
    transition: background-color 0.3s;
}

.back-btn:hover {
    background: var(--primary-dark);
}

.configurator-layout {
    display: flex;
    min-height: calc(100vh - 60px);
}

.viewer-section {
    flex: 2;
    position: relative;
    background: white;
}

.viewer-badge {
    position: absolute;
    top: 1rem;
    right: 1rem;
    z-index: 10;
    background: white;
    border-radius: 50%;
    padding: 0.5rem;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
}

.badge-360 {
    width: 3rem;
    height: 3rem;
    display: flex;
    align-items: center;
    justify-content: center;
    color: var(--primary-color);
    font-weight: bold;
    font-size: 0.8rem;
}

.viewer-canvas {
    width: 100%;
    height: calc(100vh - 120px);
    min-height: 400px;
}

.thumbnail-gallery {
    display: flex;
    gap: 0.5rem;
    padding: 1rem;
    background: white;
    border-top: 1px solid #e9ecef;
}

.thumbnail {
    width: 4rem;
    height: 4rem;
    background: #e9ecef;
    border-radius: 0.5rem;
    border: 2px solid transparent;
    cursor: pointer;
}

.thumbnail.active {
    border-color: var(--primary-color);
}

.config-panel {
    flex: 1;
    background: white;
    padding: 1.5rem;
    overflow-y: auto;
    border-left: 1px solid #e9ecef;
}

.config-section {
    margin-bottom: 2rem;
}

.location-picker {
    width: 100%;
    background: #f8f9fa;
    border: 1px solid #e9ecef;
    padding: 1rem;
    border-radius: 0.5rem;
    text-align: left;
    display: flex;
    align-items: center;
    justify-content: space-between;
    cursor: pointer;
    transition: background-color 0.3s;
}

.location-picker:hover {
    background: #e9ecef;
}

.selected-location {
    font-size: 0.8rem;
    color: #6c757d;
    margin-left: 0.5rem;
    text-overflow: ellipsis;
    white-space: nowrap;
    overflow: hidden;
}

.dimensions-section,
.colors-section {
    background: var(--text-dark);
    color: white;
    padding: 1.5rem;
    border-radius: 0.5rem;
}

.section-header {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    margin-bottom: 1rem;
    font-weight: 500;
}

.dimension-controls {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 1rem;
}

.dimension-control label {
    display: block;
    font-size: 0.8rem;
    margin-bottom: 0.5rem;
}

.dimension-value {
    font-size: 0.9rem;
    margin-bottom: 0.5rem;
}

.dimension-slider {
    width: 100%;
    margin-top: 0.5rem;
}

.color-controls {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 1rem;
    margin-bottom: 1rem;
}

.color-control label,
.media-port-control label {
    display: block;
    font-size: 0.8rem;
    margin-bottom: 0.5rem;
}

.color-select,
.media-select {
    width: 100%;
    padding: 0.5rem;
    border: 1px solid #495057;
    border-radius: 0.25rem;
    background: #495057;
    color: white;
}

.pricing-section {
    border-top: 1px solid #e9ecef;
    padding-top: 1.5rem;
}

.quantity-control {
    margin-bottom: 1rem;
}

.quantity-control label {
    display: block;
    font-weight: 500;
    margin-bottom: 0.5rem;
}

.quantity-input {
    display: flex;
    align-items: center;
    gap: 1rem;
}

.quantity-btn {
    background: var(--primary-color);
    color: white;
    border: none;
    width: 2rem;
    height: 2rem;
    border-radius: 0.25rem;
    cursor: pointer;
    font-size: 1.2rem;
    display: flex;
    align-items: center;
    justify-content: center;
}

.quantity-value {
    font-size: 1.1rem;
    font-weight: 500;
    min-width: 2rem;
    text-align: center;
}

.pricing-display {
    margin-bottom: 1.5rem;
}

.current-price {
    font-size: 2rem;
    font-weight: bold;
    color: var(--primary-color);
}

.original-price {
    font-size: 1.2rem;
    color: #6c757d;
    text-decoration: line-through;
}

.savings {
    font-size: 1rem;
    color: #28a745;
    font-weight: 500;
}

.add-to-cart-btn {
    width: 100%;
    background: var(--primary-color);
    color: white;
    border: none;
    padding: 1rem;
    border-radius: 0.5rem;
    font-size: 1.1rem;
    font-weight: 500;
    cursor: pointer;
    transition: background-color 0.3s;
}

.add-to-cart-btn:hover {
    background: var(--primary-dark);
}

/* Simplified Checkout Modal Styles */
.checkout-modal-overlay {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.6);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 1000;
    padding: 1rem;
    backdrop-filter: blur(2px);
}

.checkout-modal-simple {
    background: white;
    border-radius: 16px;
    padding: 2rem;
    max-width: 400px;
    width: 100%;
    position: relative;
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.15);
    animation: checkoutModalSlideIn 0.3s ease-out;
    text-align: center;
}

@keyframes checkoutModalSlideIn {
    from {
        opacity: 0;
        transform: translateY(-30px) scale(0.9);
    }
    to {
        opacity: 1;
        transform: translateY(0) scale(1);
    }
}

.checkout-modal-close {
    position: absolute;
    top: 1rem;
    right: 1rem;
    background: #f5f5f5;
    border: none;
    width: 32px;
    height: 32px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    font-size: 1.2rem;
    color: #666;
    transition: all 0.2s ease;
}

.checkout-modal-close:hover {
    background: #e0e0e0;
    color: #333;
}

.checkout-success-icon {
    margin: 0 auto 1.5rem auto;
    display: flex;
    align-items: center;
    justify-content: center;
}

.checkout-modal-title {
    font-size: 1.5rem;
    color: #2d3748;
    margin-bottom: 1.5rem;
    font-weight: 600;
}

.checkout-product-card {
    display: flex;
    align-items: center;
    gap: 1rem;
    background: #f8f9fa;
    padding: 1rem;
    border-radius: 12px;
    margin-bottom: 1.5rem;
    text-align: left;
}

.checkout-product-image {
    width: 60px;
    height: 60px;
    border-radius: 8px;
    object-fit: cover;
    flex-shrink: 0;
}

.checkout-product-details {
    flex: 1;
    min-width: 0;
}

.checkout-product-name {
    font-size: 1rem;
    color: #2d3748;
    margin: 0 0 0.5rem 0;
    font-weight: 600;
    line-height: 1.3;
}

.checkout-product-pricing {
    display: flex;
    align-items: center;
    gap: 0.75rem;
    margin-bottom: 0.5rem;
}

.checkout-product-price {
    font-size: 1rem;
    color: #F0B21B;
    font-weight: 600;
}

.checkout-product-quantity {
    font-size: 0.875rem;
    color: #718096;
    background: #e2e8f0;
    padding: 0.125rem 0.5rem;
    border-radius: 4px;
}

.checkout-product-total {
    font-size: 1rem;
    color: #2d3748;
    font-weight: 700;
    margin: 0;
}

.checkout-modal-buttons {
    display: flex;
    gap: 0.75rem;
    margin-top: 0.5rem;
}

.checkout-btn {
    flex: 1;
    padding: 0.75rem 1rem;
    font-weight: 600;
    border-radius: 8px;
    text-decoration: none;
    text-align: center;
    transition: all 0.2s ease;
    border: none;
    cursor: pointer;
    font-size: 0.875rem;
    min-height: 44px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.checkout-btn-secondary {
    background: #f7fafc;
    color: #4a5568;
    border: 1px solid #e2e8f0;
}

.checkout-btn-secondary:hover {
    background: #edf2f7;
    border-color: #cbd5e0;
}

.checkout-btn-primary {
    background: #F0B21B;
    color: white;
}

.checkout-btn-primary:hover {
    background: #d69e16;
    text-decoration: none;
    color: white;
}

/* Responsive Checkout Modal */
@media (max-width: 480px) {
    .checkout-modal-simple {
        padding: 1.5rem;
        margin: 1rem;
        max-width: none;
        width: calc(100% - 2rem);
    }

    .checkout-modal-title {
        font-size: 1.25rem;
        margin-bottom: 1rem;
    }

    .checkout-product-card {
        padding: 0.75rem;
        gap: 0.75rem;
    }

    .checkout-product-image {
        width: 50px;
        height: 50px;
    }

    .checkout-product-name {
        font-size: 0.875rem;
    }

    .checkout-product-pricing {
        flex-direction: column;
        align-items: flex-start;
        gap: 0.25rem;
    }

    .checkout-product-price,
    .checkout-product-total {
        font-size: 0.875rem;
    }

    .checkout-modal-buttons {
        flex-direction: column;
        gap: 0.5rem;
    }

    .checkout-btn {
        padding: 0.875rem 1rem;
        font-size: 1rem;
    }
}

/* Confirmation Modal Styles */
.confirmation-modal-overlay {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.6);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 1100;
    padding: 1rem;
    backdrop-filter: blur(4px);
}

.confirmation-modal {
    background: white;
    border-radius: 16px;
    padding: 2rem;
    max-width: 400px;
    width: 100%;
    position: relative;
    box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.25);
    animation: confirmationModalSlideIn 0.3s ease-out;
    text-align: center;
}

@keyframes confirmationModalSlideIn {
    from {
        opacity: 0;
        transform: translateY(-40px) scale(0.9);
    }
    to {
        opacity: 1;
        transform: translateY(0) scale(1);
    }
}

.confirmation-modal-close {
    position: absolute;
    top: 1rem;
    right: 1rem;
    background: #f1f5f9;
    border: none;
    width: 32px;
    height: 32px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    font-size: 1.25rem;
    color: #64748b;
    transition: all 0.2s ease;
}

.confirmation-modal-close:hover {
    background: #e2e8f0;
    color: #334155;
}

.confirmation-modal-icon {
    margin: 0 auto 1.5rem auto;
    display: flex;
    align-items: center;
    justify-content: center;
}

.confirmation-modal-content {
    margin-bottom: 2rem;
}

.confirmation-modal-title {
    font-size: 1.5rem;
    color: #1e293b;
    margin: 0 0 1rem 0;
    font-weight: 600;
}

.confirmation-modal-message {
    font-size: 1rem;
    color: #64748b;
    margin: 0;
    line-height: 1.5;
}

.confirmation-modal-actions {
    display: flex;
    gap: 0.75rem;
    justify-content: center;
}

.confirmation-btn {
    flex: 1;
    padding: 0.75rem 1.5rem;
    font-weight: 600;
    border-radius: 8px;
    border: none;
    cursor: pointer;
    font-size: 0.875rem;
    min-height: 44px;
    transition: all 0.2s ease;
    display: flex;
    align-items: center;
    justify-content: center;
}

.confirmation-btn-cancel {
    background: #f8fafc;
    color: #475569;
    border: 1px solid #e2e8f0;
}

.confirmation-btn-cancel:hover {
    background: #f1f5f9;
    border-color: #cbd5e0;
}

.confirmation-btn-confirm {
    color: white;
}

.confirmation-btn-warning {
    background: #f59e0b;
}

.confirmation-btn-warning:hover {
    background: #d97706;
}

.confirmation-btn-danger {
    background: #dc2626;
}

.confirmation-btn-danger:hover {
    background: #b91c1c;
}

.confirmation-btn-info {
    background: #2563eb;
}

.confirmation-btn-info:hover {
    background: #1d4ed8;
}

/* Responsive Confirmation Modal */
@media (max-width: 480px) {
    .confirmation-modal {
        padding: 1.5rem;
        margin: 1rem;
        max-width: none;
        width: calc(100% - 2rem);
    }

    .confirmation-modal-title {
        font-size: 1.25rem;
    }

    .confirmation-modal-message {
        font-size: 0.875rem;
    }

    .confirmation-modal-actions {
        flex-direction: column;
        gap: 0.5rem;
    }

    .confirmation-btn {
        padding: 1rem;
        font-size: 1rem;
    }
}

/* 3D Configurator Button Styles */
.btn-3d-configurator {
    background: linear-gradient(135deg, #F0B21B 0%, #e6a632 100%);
    color: white;
    border: none;
    display: flex;
    align-items: center;
    gap: 8px;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    box-shadow: 0 4px 15px rgba(240, 178, 27, 0.3);
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
}

.btn-3d-configurator::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
    transition: left 0.5s ease;
}

.btn-3d-configurator:hover::before {
    left: 100%;
}

.btn-3d-configurator:hover {
    background: linear-gradient(135deg, #e6a632 0%, #d4941f 100%);
    box-shadow: 0 6px 20px rgba(240, 178, 27, 0.4);
    transform: translateY(-2px);
}

.btn-3d-configurator svg {
    transition: transform 0.3s ease;
}

.btn-3d-configurator:hover svg {
    transform: rotateY(180deg);
}

/* Image Overlay Configurator Button */
.configurator-btn {
    background: linear-gradient(135deg, #F0B21B 0%, #e6a632 100%);
    color: white;
    border: none;
    padding: 8px 16px;
    border-radius: 6px;
    font-size: 0.9rem;
    font-weight: 600;
    display: flex;
    align-items: center;
    gap: 6px;
    box-shadow: 0 2px 10px rgba(240, 178, 27, 0.3);
    transition: all 0.3s ease;
    cursor: pointer;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.configurator-btn:hover {
    background: linear-gradient(135deg, #e6a632 0%, #d4941f 100%);
    box-shadow: 0 4px 15px rgba(240, 178, 27, 0.4);
    transform: translateY(-1px);
}

.configurator-btn svg {
    transition: transform 0.3s ease;
}

.configurator-btn:hover svg {
    transform: scale(1.1) rotateY(180deg);
}

.image-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.4);
    display: flex;
    align-items: center;
    justify-content: center;
    opacity: 0;
    transition: opacity 0.3s ease;
}

.main-image:hover .image-overlay {
    opacity: 1;
}

/* Modal Styles */
.modal-overlay {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0,0,0,0.5);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 1000;
}

.modal-content {
    background: white;
    border-radius: 0.5rem;
    max-width: 600px;
    width: 90%;
    max-height: 80vh;
    overflow: hidden;
}

.modal-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 1rem;
    border-bottom: 1px solid #e9ecef;
}

.modal-close {
    background: none;
    border: none;
    font-size: 1.5rem;
    cursor: pointer;
    color: #6c757d;
}

.modal-body {
    padding: 1rem;
}

.search-section {
    display: flex;
    gap: 0.5rem;
    margin-bottom: 1rem;
}

.address-search {
    flex: 1;
    padding: 0.5rem;
    border: 1px solid #e9ecef;
    border-radius: 0.25rem;
}

.modal-body .search-btn {
    background: var(--primary-color);
    color: white;
    border: none;
    padding: 0.5rem 1rem;
    border-radius: 0.25rem;
    cursor: pointer;
}

.map-container {
    border: 1px solid #e9ecef;
    border-radius: 0.25rem;
    margin-bottom: 1rem;
}

.selected-location-info {
    background: #f8f9fa;
    padding: 0.5rem;
    border-radius: 0.25rem;
    font-size: 0.9rem;
}

.modal-footer {
    padding: 1rem;
    border-top: 1px solid #e9ecef;
    text-align: right;
}

.confirm-btn {
    background: var(--primary-color);
    color: white;
    border: none;
    padding: 0.5rem 1rem;
    border-radius: 0.25rem;
    cursor: pointer;
}

/* Enhanced Product Detail Styles */
.product-detail-page {
    background: var(--background-light);
    min-height: 100vh;
    padding: 2rem 0;
}

.product-detail {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 3rem;
    background: var(--white);
    border-radius: 12px;
    padding: 2rem;
    box-shadow: var(--shadow);
    margin-bottom: 3rem;
}

.product-media {
    display: flex;
    flex-direction: column;
    gap: 1rem;
}

.view-mode-toggle {
    display: flex;
    gap: 0.5rem;
    margin-bottom: 1rem;
}

.view-mode-btn {
    background: var(--background-light);
    border: 2px solid var(--border-color);
    padding: 0.75rem 1.5rem;
    border-radius: 8px;
    cursor: pointer;
    font-weight: 500;
    transition: all 0.3s;
    color: var(--text-dark);
}

.view-mode-btn.active {
    background: var(--primary-color);
    border-color: var(--primary-color);
    color: var(--white);
}

.view-mode-btn:hover {
    border-color: var(--primary-color);
}

.main-media {
    position: relative;
    height: 500px;
    border-radius: 12px;
    overflow: hidden;
    background: var(--background-light);
}

.main-image {
    position: relative;
    width: 100%;
    height: 100%;
}

.main-image img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    border-radius: 12px;
}

.image-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0,0,0,0.5);
    display: flex;
    align-items: center;
    justify-content: center;
    opacity: 0;
    transition: opacity 0.3s;
}

.main-image:hover .image-overlay {
    opacity: 1;
}

.fullscreen-btn {
    background: var(--white);
    color: var(--text-dark);
    border: none;
    padding: 0.75rem 1.5rem;
    border-radius: 8px;
    cursor: pointer;
    font-weight: 500;
    transition: all 0.3s;
}

.fullscreen-btn:hover {
    background: var(--primary-color);
    color: var(--white);
}

.viewer-3d-container {
    width: 100%;
    height: 100%;
    border-radius: 12px;
    overflow: hidden;
}

.media-thumbnails {
    margin-top: 1rem;
}

.image-thumbnails {
    display: flex;
    gap: 0.75rem;
    overflow-x: auto;
    padding: 0.5rem 0;
}

.image-thumbnails img {
    width: 80px;
    height: 80px;
    object-fit: cover;
    border-radius: 8px;
    cursor: pointer;
    border: 2px solid transparent;
    transition: all 0.3s;
}

.image-thumbnails img:hover,
.image-thumbnails img.active {
    border-color: var(--primary-color);
    transform: scale(1.05);
}

.view-thumbnails {
    display: flex;
    gap: 0.75rem;
}

.view-thumb {
    background: var(--background-light);
    border: 2px solid var(--border-color);
    padding: 0.5rem 1rem;
    border-radius: 8px;
    cursor: pointer;
    font-size: 0.9rem;
    transition: all 0.3s;
}

.view-thumb.active,
.view-thumb:hover {
    background: var(--primary-color);
    border-color: var(--primary-color);
    color: var(--white);
}

.product-info {
    display: flex;
    flex-direction: column;
    gap: 1.5rem;
}

.product-info h1 {
    font-size: 2.5rem;
    color: var(--text-dark);
    margin: 0;
    line-height: 1.2;
}

.product-pricing {
    display: flex;
    align-items: center;
    gap: 1rem;
    flex-wrap: wrap;
}

.current-price {
    font-size: 2rem;
    font-weight: bold;
    color: var(--primary-color);
}

.original-price {
    font-size: 1.5rem;
    color: var(--text-light);
    text-decoration: line-through;
}

.discount-badge {
    background: var(--accent-color);
    color: var(--white);
    padding: 0.25rem 0.75rem;
    border-radius: 20px;
    font-size: 0.9rem;
    font-weight: bold;
}

.product-description {
    color: var(--text-light);
    line-height: 1.6;
    font-size: 1.1rem;
}

.product-specifications {
    background: var(--background-light);
    padding: 1.5rem;
    border-radius: 8px;
}

.product-specifications h3 {
    color: var(--text-dark);
    margin-bottom: 1rem;
    font-size: 1.3rem;
}

.product-specifications ul {
    list-style: none;
    padding: 0;
    margin: 0;
}

.product-specifications li {
    padding: 0.5rem 0;
    border-bottom: 1px solid var(--border-color);
    display: flex;
    justify-content: space-between;
}

.product-specifications li:last-child {
    border-bottom: none;
}

.product-customization {
    background: var(--background-light);
    padding: 1.5rem;
    border-radius: 8px;
}

.product-customization h3 {
    color: var(--text-dark);
    margin-bottom: 1rem;
    font-size: 1.3rem;
}

.customization-options {
    display: flex;
    flex-direction: column;
    gap: 1.5rem;
}

.option-group {
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
}

.option-group label {
    font-weight: 600;
    color: var(--text-dark);
    font-size: 0.9rem;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.color-options {
    display: flex;
    gap: 0.75rem;
}

.color-option {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    border: 3px solid transparent;
    cursor: pointer;
    transition: all 0.3s;
}

.color-option.active {
    border-color: var(--primary-color);
    transform: scale(1.1);
}

.color-option:hover {
    transform: scale(1.05);
}

.material-select {
    padding: 0.75rem;
    border: 2px solid var(--border-color);
    border-radius: 8px;
    background: var(--white);
    font-size: 1rem;
    cursor: pointer;
    transition: border-color 0.3s;
}

.material-select:focus {
    outline: none;
    border-color: var(--primary-color);
}

.quantity-control {
    display: flex;
    align-items: center;
    gap: 1rem;
    background: var(--white);
    border: 2px solid var(--border-color);
    border-radius: 8px;
    padding: 0.5rem;
    width: fit-content;
}

.quantity-btn {
    background: var(--primary-color);
    color: var(--white);
    border: none;
    width: 35px;
    height: 35px;
    border-radius: 6px;
    cursor: pointer;
    font-size: 1.2rem;
    font-weight: bold;
    transition: background-color 0.3s;
}

.quantity-btn:hover {
    background: var(--primary-dark);
}

.quantity-value {
    font-size: 1.2rem;
    font-weight: 600;
    color: var(--text-dark);
    min-width: 30px;
    text-align: center;
}

.product-actions {
    display: flex;
    flex-direction: column;
    gap: 1rem;
    margin-top: auto;
}

.btn-large {
    padding: 1rem 2rem;
    font-size: 1.1rem;
    font-weight: 600;
    border-radius: 8px;
    transition: all 0.3s;
}

.btn-primary {
    background: var(--primary-color);
    color: var(--white);
    border: none;
    cursor: pointer;
}

.btn-primary:hover {
    background: var(--primary-dark);
    transform: translateY(-2px);
}

.btn-secondary {
    background: var(--white);
    color: var(--text-dark);
    border: 2px solid var(--border-color);
    cursor: pointer;
    padding: 0.75rem 1.5rem;
    border-radius: 8px;
    font-weight: 500;
    transition: all 0.3s;
}

.btn-secondary:hover {
    border-color: var(--primary-color);
    color: var(--primary-color);
}

/* Related Products */
.related-products {
    background: var(--white);
    border-radius: 12px;
    padding: 2rem;
    box-shadow: var(--shadow);
}

.related-products h2 {
    color: var(--text-dark);
    margin-bottom: 2rem;
    font-size: 2rem;
}

.related-products-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 1.5rem;
}

.related-product-card {
    background: var(--background-light);
    border-radius: 8px;
    overflow: hidden;
    transition: transform 0.3s;
    cursor: pointer;
}

.related-product-card:hover {
    transform: translateY(-5px);
}

.related-product-image {
    height: 200px;
    overflow: hidden;
}

.related-product-image img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.related-product-info {
    padding: 1rem;
}

.related-product-info h4 {
    color: var(--text-dark);
    margin-bottom: 0.5rem;
}

.related-product-info p {
    color: var(--primary-color);
    font-weight: bold;
    font-size: 1.1rem;
}

/* Fullscreen 3D Modal */
.modal-overlay {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0,0,0,0.8);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 1000;
}

.modal-content.fullscreen-3d {
    width: 90vw;
    height: 90vh;
    background: var(--white);
    border-radius: 12px;
    display: flex;
    flex-direction: column;
    overflow: hidden;
}

.modal-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 1rem 1.5rem;
    border-bottom: 1px solid var(--border-color);
    background: var(--background-light);
}

.modal-header h3 {
    color: var(--text-dark);
    margin: 0;
}

.modal-close {
    background: none;
    border: none;
    font-size: 2rem;
    cursor: pointer;
    color: var(--text-light);
    transition: color 0.3s;
}

.modal-close:hover {
    color: var(--text-dark);
}

.modal-body {
    flex: 1;
    position: relative;
}

.modal-footer {
    padding: 1rem 1.5rem;
    border-top: 1px solid var(--border-color);
    background: var(--background-light);
    text-align: center;
}

.viewer-info p {
    color: var(--text-light);
    margin: 0;
    font-size: 0.9rem;
}

/* Responsive Design */
@media (max-width: 768px) {
    .desktop-nav {
        display: none;
    }

    .mobile-menu-toggle {
        display: flex;
    }

    .mobile-filter-toggle {
        display: block;
    }

    .product-filter {
        padding: 1.5rem;
        margin-bottom: 1.5rem;
    }

    .filter-header {
        margin-bottom: 1.5rem;
        padding-bottom: 0.75rem;
    }

    .filter-header h3 {
        font-size: 1.2rem;
    }

    .filter-section {
        margin-bottom: 1.5rem;
        padding-bottom: 1rem;
    }

    .filter-section label {
        font-size: 0.95rem;
        margin-bottom: 0.5rem;
    }

    .filter-section input,
    .filter-section select {
        padding: 0.875rem;
        font-size: 0.9rem;
    }

    .filter-content {
        display: none;
    }

    .filter-content.open {
        display: block;
    }

    .product-actions {
        flex-direction: column;
    }

    .product-actions .btn {
        flex: none;
    }

    .configurator-layout {
        flex-direction: column;
    }

    .viewer-section {
        flex: none;
    }

    .viewer-canvas {
        height: 50vh;
    }

    .config-panel {
        border-left: none;
        border-top: 1px solid #e9ecef;
    }

    .dimension-controls,
    .color-controls {
        grid-template-columns: 1fr;
    }

    /* Enhanced Product Detail Responsive */
    .product-detail {
        grid-template-columns: 1fr;
        gap: 2rem;
        padding: 1.5rem;
    }

    .main-media {
        height: 300px;
    }

    .view-mode-toggle {
        justify-content: center;
    }

    .view-mode-btn {
        flex: 1;
        text-align: center;
    }

    .product-info h1 {
        font-size: 2rem;
    }

    .current-price {
        font-size: 1.5rem;
    }

    .original-price {
        font-size: 1.2rem;
    }

    .customization-options {
        gap: 1rem;
    }

    .color-options {
        justify-content: center;
    }

    .product-actions {
        gap: 0.75rem;
    }

    .related-products-grid {
        grid-template-columns: 1fr;
    }

    .modal-content.fullscreen-3d {
        width: 95vw;
        height: 95vh;
    }

    .modal-header {
        padding: 0.75rem 1rem;
    }

    .modal-header h3 {
        font-size: 1.2rem;
    }
}

@media (max-width: 480px) {
    .product-detail-page {
        padding: 1rem 0;
    }

    .product-detail {
        padding: 1rem;
        margin-bottom: 2rem;
    }

    .view-mode-toggle {
        flex-direction: column;
        gap: 0.5rem;
    }

    .main-media {
        height: 250px;
    }

    .product-info h1 {
        font-size: 1.5rem;
    }

    .current-price {
        font-size: 1.3rem;
    }

    .color-options {
        flex-wrap: wrap;
    }

    .color-option {
        width: 35px;
        height: 35px;
    }

    .related-products {
        padding: 1rem;
    }

    .related-products h2 {
        font-size: 1.5rem;
        margin-bottom: 1rem;
    }
}

/* Shopping Cart Styles */
.cart-icon-container {
    position: relative;
}

.cart-icon-button {
    background: none;
    border: none;
    cursor: pointer;
    padding: 0.5rem;
    border-radius: 8px;
    transition: all 0.3s;
    position: relative;
    color: var(--text-dark);
}

.cart-icon-button:hover {
    background: var(--background-light);
    color: var(--primary-color);
}

.cart-icon-button:hover .cart-icon path {
    stroke: #e6a632;
}

.cart-icon {
    width: 24px;
    height: 24px;
}

.cart-badge {
    position: absolute;
    top: 0;
    right: 0;
    background: var(--accent-color);
    color: var(--white);
    border-radius: 50%;
    width: 20px;
    height: 20px;
    font-size: 0.75rem;
    font-weight: bold;
    display: flex;
    align-items: center;
    justify-content: center;
    transform: translate(25%, -25%);
    animation: cartBadgePulse 0.3s ease-out;
}

@keyframes cartBadgePulse {
    0% { transform: translate(25%, -25%) scale(0.8); }
    50% { transform: translate(25%, -25%) scale(1.2); }
    100% { transform: translate(25%, -25%) scale(1); }
}

/* Cart Sidebar */
.cart-overlay {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.5);
    z-index: 999;
    opacity: 0;
    animation: fadeIn 0.3s ease-out forwards;
}

@keyframes fadeIn {
    to { opacity: 1; }
}

.cart-sidebar {
    position: fixed;
    top: 0;
    right: 0;
    width: 320px; /* Reduced from 400px (20% reduction) */
    height: 100vh;
    background: var(--white);
    box-shadow: -4px 0 20px rgba(0, 0, 0, 0.1);
    z-index: 1000;
    transform: translateX(100%);
    transition: transform 0.3s ease-out;
    display: flex;
    flex-direction: column;
}

.cart-sidebar.open {
    transform: translateX(0);
}

.cart-header {
    display: flex;
    justify-content: center;
    align-items: center;
    padding: 1rem; /* Reduced from 1.5rem */
    border-bottom: 1px solid var(--border-color);
    background: var(--background-light);
    position: relative;
}

.cart-header h2 {
    margin: 0;
    color: var(--text-dark);
    font-size: 1.25rem; /* Reduced from 1.5rem */
    text-align: center;
}

.cart-close-btn {
    background: none;
    border: none;
    font-size: 1.5rem; /* Reduced from 2rem */
    cursor: pointer;
    color: var(--text-light);
    transition: color 0.3s;
    padding: 0;
    width: 32px; /* Reduced from 40px */
    height: 32px; /* Reduced from 40px */
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 50%;
    position: absolute;
    right: 1rem; /* Reduced from 1.5rem */
    top: 50%;
    transform: translateY(-50%);
}

.cart-close-btn:hover {
    color: var(--text-dark);
    background: var(--white);
}

.cart-content {
    flex: 1;
    display: flex;
    flex-direction: column;
    overflow: hidden;
}

.cart-empty {
    flex: 1;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 1.5rem; /* Reduced from 2rem for compact sidebar */
    text-align: center;
}

.empty-cart-icon {
    font-size: 4rem;
    margin-bottom: 1rem;
    opacity: 0.5;
}

.cart-empty h3 {
    color: var(--text-dark);
    margin-bottom: 0.5rem;
}

.cart-empty p {
    color: var(--text-light);
    margin-bottom: 1.5rem; /* Reduced from 2rem for compact design */
}

.cart-items {
    flex: 1;
    overflow-y: auto;
    padding: 0.75rem; /* Reduced from 1rem */
}

.cart-item {
    display: flex;
    gap: 1.25rem; /* Reduced from 1.75rem */
    padding: 1.5rem 0; /* Reduced from 2rem */
    border-bottom: 1px solid #f1f5f9;
    transition: all 0.3s ease;
    position: relative;
}

.cart-item:hover {
    background: rgba(248, 250, 252, 0.5);
    border-radius: 12px;
    margin: 0 -0.75rem; /* Adjusted for new padding */
    padding: 1.5rem 0.75rem; /* Adjusted for new padding */
}

.cart-item:last-child {
    border-bottom: none;
    padding-bottom: 0;
}

.cart-item-image {
    flex-shrink: 0;
    position: relative;
    overflow: hidden;
    border-radius: 12px;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.cart-item-image img {
    width: 80px; /* Reduced from 100px */
    height: 80px; /* Reduced from 100px */
    object-fit: cover;
    border-radius: 10px; /* Slightly reduced from 12px */
    transition: transform 0.3s ease;
}

.cart-item-image:hover img {
    transform: scale(1.05);
}

.cart-item-details {
    flex: 1;
    display: flex;
    flex-direction: column;
    gap: 0.75rem; /* Reduced from 1rem */
    min-height: 80px; /* Reduced from 100px to match image */
    justify-content: space-between;
}

.cart-item-info {
    display: flex;
    flex-direction: column;
    gap: 0.5rem; /* Reduced from 0.75rem */
}

.cart-item-name {
    color: #1e293b;
    font-weight: 600;
    text-decoration: none;
    font-size: 1rem; /* Reduced from 1.125rem (11% reduction) */
    line-height: 1.4;
    letter-spacing: -0.025em;
    transition: color 0.3s ease;
    margin-bottom: 0.25rem;
}

.cart-item-name:hover {
    color: #F0B21B;
}

.cart-item-customization {
    font-size: 0.75rem; /* Reduced from 0.875rem (14% reduction) */
    color: #64748b;
    line-height: 1.5;
    background: #f8fafc;
    padding: 0.5rem 0.75rem; /* Reduced padding */
    border-radius: 6px; /* Slightly reduced */
    border-left: 3px solid #F0B21B;
    font-weight: 500;
    letter-spacing: -0.025em;
    margin-top: 0.25rem;
}

.cart-item-price {
    font-weight: 700;
    color: #F0B21B;
    font-size: 1rem; /* Reduced from 1.125rem (11% reduction) */
    letter-spacing: -0.025em;
    margin-top: 0.5rem;
}

.cart-item-controls {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-top: auto;
    gap: 1rem; /* Reduced from 1.25rem */
    padding-top: 0.5rem; /* Reduced from 0.75rem */
}

.quantity-controls {
    display: flex;
    align-items: center;
    gap: 0.75rem;
    background: #f8fafc;
    border-radius: 10px;
    padding: 0.5rem 0.75rem;
    border: 1px solid #e2e8f0;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
}

.quantity-btn {
    background: white;
    border: 1px solid #e2e8f0;
    width: 28px; /* Reduced from 34px */
    height: 28px; /* Reduced from 34px */
    border-radius: 6px; /* Reduced from 8px */
    cursor: pointer;
    font-size: 0.875rem; /* Reduced from 1rem */
    font-weight: 600;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.3s ease;
    color: #64748b;
}

.quantity-btn:hover {
    background: #F0B21B;
    color: white;
    border-color: #F0B21B;
    transform: translateY(-1px);
    box-shadow: 0 4px 8px rgba(240, 178, 27, 0.3);
}

.quantity-display {
    font-weight: 700;
    min-width: 28px; /* Reduced from 32px */
    text-align: center;
    font-size: 0.875rem; /* Reduced from 1rem */
    color: #1e293b;
    letter-spacing: -0.025em;
    line-height: 1.2;
}

.cart-item-actions {
    display: flex;
    flex-direction: column;
    align-items: flex-end;
    gap: 0.75rem;
}

.cart-item-total {
    font-weight: 700;
    color: #1e293b;
    font-size: 1.1rem; /* Reduced from 1.25rem (12% reduction) */
    letter-spacing: -0.025em;
    text-align: right;
}

.remove-btn {
    background: #fee2e2;
    border: 1px solid #fecaca;
    color: #dc2626;
    cursor: pointer;
    padding: 0.375rem; /* Reduced from 0.5rem */
    border-radius: 6px; /* Reduced from 8px */
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    justify-content: center;
    width: 30px; /* Reduced from 36px */
    height: 30px; /* Reduced from 36px */
}

.remove-btn:hover {
    background: #dc2626;
    color: white;
    border-color: #dc2626;
    transform: translateY(-1px);
    box-shadow: 0 4px 8px rgba(220, 38, 38, 0.3);
}

.cart-summary {
    padding: 1rem; /* Reduced from 1.5rem */
    border-top: 1px solid var(--border-color);
    background: var(--background-light);
}

.summary-row {
    display: flex;
    justify-content: space-between;
    margin-bottom: 0.5rem; /* Reduced from 0.75rem */
    font-size: 0.8rem; /* Reduced from 0.9rem */
}

.summary-row.total {
    font-weight: bold;
    font-size: 1rem; /* Reduced from 1.1rem */
    color: var(--text-dark);
    border-top: 1px solid var(--border-color);
    padding-top: 0.5rem; /* Reduced from 0.75rem */
    margin-top: 0.5rem; /* Reduced from 0.75rem */
}

.free-shipping-notice {
    background: var(--success-color);
    color: var(--white);
    padding: 0.5rem;
    border-radius: 6px;
    font-size: 0.8rem;
    text-align: center;
    margin: 0.5rem 0;
}

.cart-actions {
    padding: 1rem; /* Reduced from 1.5rem */
    display: flex;
    flex-direction: column;
    gap: 0.5rem; /* Reduced from 0.75rem */
}

.btn-full {
    width: 100%;
    text-align: center;
}

.btn-text {
    background: none;
    border: none;
    color: var(--text-light);
    cursor: pointer;
    padding: 0.75rem;
    font-size: 0.9rem;
    transition: color 0.3s;
}

.btn-text:hover {
    color: var(--accent-color);
}

/* Cart Page Styles */
.cart-page {
    background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
    min-height: 100vh;
    padding: 2.5rem 0;
    font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    line-height: 1.6;
    letter-spacing: -0.025em;
}

.cart-empty-page {
    display: flex;
    align-items: center;
    justify-content: center;
    min-height: 60vh;
}

.empty-cart-content {
    text-align: center;
    max-width: 480px;
    background: white;
    padding: 3rem 2rem;
    border-radius: 20px;
    box-shadow: 0 10px 25px -5px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
    border: 1px solid rgba(226, 232, 240, 0.8);
}

.empty-cart-content .empty-cart-icon {
    font-size: 5rem;
    margin-bottom: 1.5rem;
    opacity: 0.4;
    filter: grayscale(0.3);
}

.empty-cart-content h1 {
    color: #1e293b;
    margin-bottom: 1rem;
    font-size: 2.25rem;
    font-weight: 700;
    line-height: 1.2;
    letter-spacing: -0.025em;
}

.empty-cart-content p {
    color: #64748b;
    margin-bottom: 2.5rem;
    font-size: 1.125rem;
    line-height: 1.6;
    font-weight: 400;
}

.empty-cart-actions {
    display: flex;
    flex-direction: column;
    gap: 1rem;
    align-items: center;
}

.empty-cart-actions .btn {
    min-width: 60px; /* Reduced from 200px (20% reduction) */
    padding: 0.75rem 1.5rem; /* Reduced from 0.875rem 2rem */
    font-weight: 600;
    font-size: 0.9rem; /* Reduced from 1rem */
    border-radius: 8px; /* Reduced from 12px for cleaner look */
    transition: all 0.3s ease;
    text-decoration: none;
    display: inline-flex;
    align-items: center;
    justify-content: center;
    letter-spacing: -0.025em;
}

.empty-cart-actions .btn-primary {
    background: linear-gradient(135deg, #F0B21B 0%, #e6a017 100%);
    color: white;
    border: none;
    box-shadow: 0 2px 8px 0 rgba(240, 178, 27, 0.2); /* More subtle shadow */
}

.empty-cart-actions .btn-primary:hover {
    transform: translateY(-1px); /* Reduced from -2px for more refined effect */
    box-shadow: 0 4px 12px 0 rgba(240, 178, 27, 0.3); /* More refined shadow */
}

.empty-cart-actions .btn-secondary {
    background: white;
    color: #64748b;
    border: 2px solid #e2e8f0;
}

.empty-cart-actions .btn-secondary:hover {
    border-color: #F0B21B;
    color: #F0B21B;
    transform: translateY(-1px);
}

/* Compact cart sidebar optimization */
.cart-sidebar .empty-cart-actions .btn {
    min-width: 50px; /* Optimized for 320px sidebar width */
    margin: 0 auto; /* Center the button */
}

/* Cart Page Breadcrumb */
.cart-breadcrumb {
    margin-bottom: 2rem;
    padding: 1rem 0;
}

.breadcrumb-list {
    display: flex;
    align-items: center;
    list-style: none;
    margin: 0;
    padding: 0;
    gap: 0.5rem;
}

.breadcrumb-item {
    display: flex;
    align-items: center;
}

.breadcrumb-link {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    color: #F0B21B;
    text-decoration: none;
    font-weight: 500;
    font-size: 0.875rem;
    padding: 0.5rem 0.75rem;
    border-radius: 6px;
    transition: all 0.2s ease;
}

.breadcrumb-link:hover {
    background: rgba(240, 178, 27, 0.1);
    color: #d69e16;
}

.breadcrumb-link svg {
    width: 14px;
    height: 14px;
}

.breadcrumb-separator {
    display: flex;
    align-items: center;
    color: #cbd5e0;
    margin: 0 0.25rem;
}

.breadcrumb-separator svg {
    width: 12px;
    height: 12px;
}

.breadcrumb-current {
    color: #4a5568;
    font-weight: 600;
    font-size: 0.875rem;
    padding: 0.5rem 0.75rem;
}

/* Cart Page Header */
.cart-page-header {
    background: linear-gradient(135deg, #ffffff 0%, #f8fafc 100%);
    border: 1px solid #e2e8f0;
    border-radius: 20px;
    padding: 3rem 2rem;
    margin-bottom: 2.5rem;
    text-align: center;
    box-shadow: 0 10px 25px -5px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
    position: relative;
    overflow: hidden;
}

.cart-page-header::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: linear-gradient(90deg, #F0B21B 0%, #e6a017 100%);
}

.cart-header-content {
    max-width: 600px;
    margin: 0 auto;
    position: relative;
    z-index: 1;
}

.cart-title {
    color: #1e293b;
    font-size: 2.75rem;
    font-weight: 800;
    margin: 0 0 1.5rem 0;
    line-height: 1.1;
    letter-spacing: -0.05em;
    background: linear-gradient(135deg, #1e293b 0%, #475569 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    position: relative;
}

.cart-title::after {
    content: '';
    position: absolute;
    bottom: -1rem;
    left: 50%;
    transform: translateX(-50%);
    width: 80px;
    height: 4px;
    background: linear-gradient(90deg, #F0B21B 0%, #e6a017 100%);
    border-radius: 2px;
}

.cart-subtitle {
    color: #64748b;
    font-size: 1.125rem;
    margin: 0.75rem 0 0 0;
    font-weight: 500;
    line-height: 1.6;
    letter-spacing: -0.025em;
}

.cart-layout {
    display: grid;
    grid-template-columns: 1fr 380px;
    gap: 2.5rem;
    align-items: start;
}

.cart-main {
    background: white;
    border-radius: 16px;
    padding: 2.5rem;
    box-shadow: 0 10px 25px -5px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
    border: 1px solid rgba(226, 232, 240, 0.8);
    transition: box-shadow 0.3s ease;
}

.cart-main:hover {
    box-shadow: 0 20px 40px -10px rgba(0, 0, 0, 0.15), 0 8px 16px -4px rgba(0, 0, 0, 0.1);
}

.cart-items-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 2.5rem;
    padding-bottom: 1.5rem;
    border-bottom: 2px solid #f1f5f9;
    position: relative;
}

.cart-items-header::after {
    content: '';
    position: absolute;
    bottom: -2px;
    left: 0;
    width: 60px;
    height: 2px;
    background: linear-gradient(90deg, #F0B21B 0%, #e6a017 100%);
    border-radius: 1px;
}

.cart-items-header h2 {
    color: #1e293b;
    margin: 0;
    font-size: 1.75rem;
    font-weight: 700;
    letter-spacing: -0.025em;
    line-height: 1.2;
}

.clear-cart-btn {
    background: none;
    border: 2px solid #F0B21B;
    color: #F0B21B;
    padding: 0.75rem 1.5rem;
    border-radius: 10px;
    cursor: pointer;
    font-size: 0.875rem;
    font-weight: 600;
    transition: all 0.3s ease;
    letter-spacing: -0.025em;
}

.clear-cart-btn:hover {
    background: #F0B21B;
    color: white;
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(240, 178, 27, 0.3);
}

.cart-items-list {
    margin-bottom: 2rem;
}

.cart-item-wrapper {
    border-bottom: 1px solid var(--border-color);
    padding: 1.5rem 0;
}

.cart-item-wrapper:last-child {
    border-bottom: none;
}

.cart-item-wrapper .cart-item {
    padding: 0;
    border-bottom: none;
}

.cart-item-wrapper .cart-item-image img {
    width: 120px;
    height: 120px;
}

.cart-item-wrapper .cart-item-name {
    font-size: 1.1rem;
    margin-bottom: 0.5rem;
}

.cart-item-wrapper .cart-item-price {
    font-size: 1rem;
}

.cart-item-wrapper .cart-item-total {
    font-size: 1.1rem;
}

.cart-actions-bottom {
    padding-top: 2rem;
    border-top: 1px solid var(--border-color);
}

.cart-sidebar-summary {
    display: flex;
    flex-direction: column;
    gap: 2rem;
}

.cart-summary-card {
    background: white;
    border-radius: 20px;
    padding: 2.75rem;
    box-shadow: 0 10px 25px -5px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
    border: 1px solid rgba(226, 232, 240, 0.8);
    position: sticky;
    top: 2rem;
    transition: box-shadow 0.3s ease;
}

.cart-summary-card:hover {
    box-shadow: 0 20px 40px -10px rgba(0, 0, 0, 0.15), 0 8px 16px -4px rgba(0, 0, 0, 0.1);
}

.cart-summary-card h3 {
    color: #1e293b;
    margin-bottom: 2.5rem;
    font-size: 1.75rem;
    font-weight: 700;
    letter-spacing: -0.05em;
    line-height: 1.2;
    line-height: 1.2;
    background: linear-gradient(135deg, #1e293b 0%, #475569 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    position: relative;
}

.cart-summary-card h3::after {
    content: '';
    position: absolute;
    bottom: -1rem;
    left: 0;
    width: 50px;
    height: 3px;
    background: linear-gradient(90deg, #F0B21B 0%, #e6a017 100%);
    border-radius: 2px;
}

.summary-details {
    margin-bottom: 2.5rem;
}

.shipping-notice {
    background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
    padding: 1rem 1.25rem;
    border-radius: 12px;
    font-size: 0.875rem;
    color: #64748b;
    text-align: center;
    margin: 1rem 0;
    border: 1px solid rgba(226, 232, 240, 0.6);
    font-weight: 500;
    letter-spacing: -0.025em;
}

.free-shipping {
    color: #10b981;
    font-weight: 700;
    text-transform: uppercase;
    font-size: 0.75rem;
    letter-spacing: 0.05em;
}

.checkout-actions {
    display: flex;
    flex-direction: column;
    gap: 1rem;
}

.payment-methods {
    text-align: center;
    margin-top: 1rem;
}

.payment-methods p {
    color: var(--text-light);
    font-size: 0.9rem;
    margin-bottom: 0.5rem;
}

.payment-icons {
    display: flex;
    justify-content: center;
    gap: 0.5rem;
    font-size: 1.5rem;
}

.recommended-products {
    background: var(--white);
    border-radius: 12px;
    padding: 1.5rem;
    box-shadow: var(--shadow);
}

.recommended-products h4 {
    color: var(--text-dark);
    margin-bottom: 1rem;
    font-size: 1.2rem;
}

.recommended-item {
    display: flex;
    gap: 1rem;
    align-items: center;
    padding: 1rem;
    border-radius: 8px;
    transition: background-color 0.3s;
    cursor: pointer;
}

.recommended-item:hover {
    background: var(--background-light);
}

.recommended-item img {
    width: 60px;
    height: 60px;
    object-fit: cover;
    border-radius: 6px;
}

.recommended-info p {
    margin: 0 0 0.25rem 0;
    font-weight: 600;
    color: var(--text-dark);
    font-size: 0.9rem;
}

.recommended-info span {
    color: var(--primary-color);
    font-weight: bold;
    font-size: 0.9rem;
}

/* Cart Responsive Styles */
@media (max-width: 768px) {
    .cart-sidebar {
        width: 100vw; /* Maintain full-width on mobile */
    }

    /* Reduce internal spacing on mobile */
    .cart-header {
        padding: 0.75rem;
    }

    .cart-items {
        padding: 0.5rem;
    }

    .cart-item {
        padding: 1rem 0;
        gap: 1rem;
    }

    .cart-summary {
        padding: 0.75rem;
    }

    .cart-actions {
        padding: 0.75rem;
    }

    /* Mobile empty cart button - maintain accessibility */
    .empty-cart-actions .btn {
        min-width: 140px; /* Slightly smaller on mobile but maintain usability */
        padding: 0.875rem 1.25rem; /* Ensure 44px minimum touch target height */
        font-size: 0.9rem;
    }
}

@media (max-width: 768px) {
    .cart-layout {
        grid-template-columns: 1fr;
        gap: 1rem;
    }

    .cart-main,
    .cart-summary-card {
        padding: 1.5rem;
    }

    /* Cart Page Responsive */
    .cart-breadcrumb {
        margin-bottom: 1.5rem;
        padding: 0.75rem 0;
    }

    .breadcrumb-link {
        font-size: 0.8rem;
        padding: 0.375rem 0.5rem;
        gap: 0.375rem;
    }

    .breadcrumb-link svg {
        width: 12px;
        height: 12px;
    }

    .breadcrumb-current {
        font-size: 0.8rem;
        padding: 0.375rem 0.5rem;
    }

    .cart-page-header {
        padding: 1.5rem 1rem;
        margin-bottom: 1.5rem;
        border-radius: 12px;
    }

    .cart-title {
        font-size: 1.875rem;
        margin-bottom: 0.5rem;
    }

    .cart-subtitle {
        font-size: 1rem;
    }
}

/* Extra small screens */
@media (max-width: 375px) {
    .cart-breadcrumb {
        margin-bottom: 1rem;
    }

    .breadcrumb-link {
        font-size: 0.75rem;
        padding: 0.25rem 0.375rem;
    }

    .breadcrumb-current {
        font-size: 0.75rem;
        padding: 0.25rem 0.375rem;
    }

    .cart-page-header {
        padding: 1.25rem 0.75rem;
        margin-bottom: 1.25rem;
    }

    .cart-title {
        font-size: 1.625rem;
    }

    .cart-subtitle {
        font-size: 0.875rem;
    }
}

@media (max-width: 768px) {
    .cart-items-header {
        flex-direction: column;
        gap: 1rem;
        align-items: flex-start;
    }

    .cart-item-wrapper .cart-item {
        flex-direction: column;
        gap: 1rem;
    }

    .cart-item-wrapper .cart-item-image {
        align-self: center;
    }

    .cart-item-wrapper .cart-item-details {
        text-align: center;
    }

    .cart-item-controls {
        justify-content: center;
        gap: 2rem;
    }

    .empty-cart-actions {
        flex-direction: column;
        width: 100%;
    }
}

/* ===== CART SIDEBAR RESPONSIVE BREAKPOINTS ===== */

/* Tablet (768px - 1199px) - Cart sidebar responsive */
@media (min-width: 768px) and (max-width: 1199px) {
    .cart-sidebar {
        width: 280px; /* Reduced for tablet */
    }
}

/* Desktop Large (1200px+) - Cart sidebar responsive */
@media (min-width: 1200px) {
    .cart-sidebar {
        width: 300px; /* Smallest width for desktop */
    }
}

/* ===== UNIFIED HEADER RESPONSIVE ENHANCEMENTS ===== */

/* Desktop Large (1200px+) - Enhanced unified header scroll behavior */
@media (min-width: 1200px) {
    .unified-header-container.unified-floating {
        box-shadow: 0 6px 30px rgba(0, 0, 0, 0.2);
    }

    .unified-header-container.unified-floating .main-header {
        background: rgba(255, 255, 255, 0.92);
        backdrop-filter: blur(12px);
        -webkit-backdrop-filter: blur(12px);
        border-bottom: none; /* Seamless floating appearance */
        margin-bottom: 0;
        padding-bottom: 1rem;
    }

    .unified-header-container.unified-floating .navigation-bar {
        background: rgba(240, 178, 27, 0.92);
    }

    .unified-header-container.unified-floating .nav-link {
        padding: 1.2rem 2rem;
        font-size: 1rem;
    }

    .unified-header-container.unified-floating .nav-link:hover {
        background-color: rgba(255, 255, 255, 0.2);
        transform: translateY(-2px);
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    }
}

/* Tablet (768px - 1199px) - Optimized unified header scroll behavior */
@media (min-width: 768px) and (max-width: 1199px) {
    .unified-header-container.unified-floating {
        box-shadow: 0 4px 20px rgba(0, 0, 0, 0.18);
    }

    .unified-header-container.unified-floating .main-header {
        background: rgba(255, 255, 255, 0.94);
        backdrop-filter: blur(10px);
        -webkit-backdrop-filter: blur(10px);
        border-bottom: none; /* Seamless floating appearance */
        margin-bottom: 0;
        padding-bottom: 1rem;
    }

    .unified-header-container.unified-floating .navigation-bar {
        background: rgba(240, 178, 27, 0.94);
    }

    .unified-header-container.unified-floating .nav-link {
        padding: 1rem 1.5rem;
        font-size: 0.9rem;
    }

    .unified-header-container.unified-floating .nav-link:hover {
        background-color: rgba(255, 255, 255, 0.18);
        transform: translateY(-1px);
    }
}

/* Mobile (320px - 767px) - Touch-optimized unified header scroll behavior */
@media (max-width: 767px) {
    .unified-header-container.unified-floating {
        box-shadow: 0 3px 15px rgba(0, 0, 0, 0.2);
    }

    .unified-header-container.unified-floating .main-header {
        background: rgba(255, 255, 255, 0.96);
        backdrop-filter: blur(8px);
        -webkit-backdrop-filter: blur(8px);
        border-bottom: none; /* Seamless floating appearance */
        margin-bottom: 0;
        padding-bottom: 1rem;
    }

    .unified-header-container.unified-floating .navigation-bar {
        background: rgba(240, 178, 27, 0.96);
    }

    .unified-header-container.unified-floating .nav-link {
        padding: 0.9rem 1.2rem;
        font-size: 0.85rem;
        min-height: 44px;
        display: flex;
        align-items: center;
        justify-content: center;
    }

    .unified-header-container.unified-floating .nav-link:hover,
    .unified-header-container.unified-floating .nav-link:active {
        background-color: rgba(255, 255, 255, 0.15);
        transform: none; /* Disable transform on mobile for better touch experience */
    }

    /* Enhanced mobile unified header scroll behavior */
    .unified-header-container.unified-hidden {
        transform: translateY(-100%);
        transition: transform 0.25s cubic-bezier(0.4, 0, 0.2, 1);
    }
}

/* Extra Small Mobile (320px - 479px) - Compact unified header scroll behavior */
@media (max-width: 479px) {
    .unified-header-container.unified-floating .main-header {
        background: rgba(255, 255, 255, 0.98);
        backdrop-filter: blur(6px);
        -webkit-backdrop-filter: blur(6px);
        border-bottom: none; /* Seamless floating appearance */
        margin-bottom: 0;
        padding-bottom: 1rem;
    }

    .unified-header-container.unified-floating .navigation-bar {
        background: rgba(240, 178, 27, 0.98);
    }

    .unified-header-container.unified-floating .nav-link {
        padding: 0.8rem 1rem;
        font-size: 0.8rem;
        letter-spacing: 0.3px;
    }

    .unified-header-container.unified-floating {
        box-shadow: 0 2px 12px rgba(0, 0, 0, 0.25);
    }
}

/* Performance optimizations for unified header scroll animations */
.unified-header-container {
    will-change: transform;
    backface-visibility: hidden;
    -webkit-backface-visibility: hidden;
}

/* Reduced motion preferences */
@media (prefers-reduced-motion: reduce) {
    .unified-header-container {
        transition: none;
    }

    .unified-header-container.unified-floating .nav-link:hover {
        transform: none;
    }
}
