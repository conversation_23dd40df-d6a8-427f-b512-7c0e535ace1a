const express = require('express');
const { body, validationResult } = require('express-validator');
const { PaymentService } = require('../services/paymentService');
const { authenticateToken: auth, requireAdmin } = require('../middleware/auth');
const router = express.Router();

// Create payment service instance
const paymentService = new PaymentService();

// Mock payment data
const payments = [
  {
    id: 'PAY001',
    orderId: 'ORD001',
    amount: 696.98,
    currency: 'PHP',
    status: 'Completed',
    method: 'Credit Card',
    transactionId: 'TXN123456789',
    processedAt: new Date().toISOString(),
    createdAt: new Date().toISOString()
  }
];

// @route   GET /api/payments
// @desc    Get all payments
// @access  Private
router.get('/', async (req, res) => {
  try {
    const { page = 1, limit = 10, status, orderId } = req.query;
    
    let filteredPayments = [...payments];
    
    if (status) {
      filteredPayments = filteredPayments.filter(payment => payment.status === status);
    }
    
    if (orderId) {
      filteredPayments = filteredPayments.filter(payment => payment.orderId === orderId);
    }
    
    const startIndex = (page - 1) * limit;
    const endIndex = page * limit;
    const paginatedPayments = filteredPayments.slice(startIndex, endIndex);
    
    res.json({
      success: true,
      data: {
        payments: paginatedPayments,
        pagination: {
          currentPage: parseInt(page),
          totalPages: Math.ceil(filteredPayments.length / limit),
          totalItems: filteredPayments.length,
          itemsPerPage: parseInt(limit)
        }
      }
    });
    
  } catch (error) {
    console.error('Get payments error:', error);
    res.status(500).json({
      success: false,
      message: 'Server error'
    });
  }
});

// @route   GET /api/payments/:id
// @desc    Get payment by ID
// @access  Private
router.get('/:id', async (req, res) => {
  try {
    const payment = payments.find(p => p.id === req.params.id);
    
    if (!payment) {
      return res.status(404).json({
        success: false,
        message: 'Payment not found'
      });
    }
    
    res.json({
      success: true,
      data: payment
    });
    
  } catch (error) {
    console.error('Get payment error:', error);
    res.status(500).json({
      success: false,
      message: 'Server error'
    });
  }
});

// @route   POST /api/payments/create-link
// @desc    Create PayMongo payment link for order
// @access  Public (Guest checkout allowed)
router.post('/create-link', [
  // Remove auth requirement for guest checkout
  body('orderId').notEmpty().withMessage('Order ID is required'),
  body('totalAmount').isFloat({ min: 1 }).withMessage('Total amount must be at least PHP 1.00'),
  body('items').optional().isArray().withMessage('Items must be an array'),
  body('customer.email').optional().isEmail().withMessage('Valid email is required'),
  body('customer.name').optional().notEmpty().withMessage('Customer name is required')
], async (req, res) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        success: false,
        message: 'Validation errors',
        errors: errors.array()
      });
    }

    const { orderId, totalAmount, items, customer, shippingAddress, metadata } = req.body;

    // Create payment link using PayMongo service
    const result = await paymentService.createOrderPaymentLink({
      orderId,
      totalAmount,
      items: items || [],
      customer: customer || {},
      shippingAddress: shippingAddress || {}
    }, { metadata: metadata || {} });

    if (!result.success) {
      return res.status(400).json({
        success: false,
        message: result.error || 'Failed to create payment link',
        code: result.code
      });
    }

    res.status(201).json({
      success: true,
      message: 'Payment link created successfully',
      data: result.data
    });

  } catch (error) {
    console.error('Create payment link error:', error);
    res.status(500).json({
      success: false,
      message: 'Server error while creating payment link'
    });
  }
});

// @route   POST /api/payments/process
// @desc    Process payment (legacy endpoint for backward compatibility)
// @access  Private
router.post('/process', [
  body('orderId').notEmpty(),
  body('amount').isFloat({ min: 0 }),
  body('method').isIn(['Credit Card', 'Debit Card', 'Bank Transfer', 'Cash', 'PayMongo'])
], async (req, res) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        success: false,
        message: 'Validation errors',
        errors: errors.array()
      });
    }

    const { orderId, amount, method, currency = 'PHP' } = req.body;
    
    // Mock payment processing
    const newPayment = {
      id: `PAY${String(payments.length + 1).padStart(3, '0')}`,
      orderId,
      amount: parseFloat(amount),
      currency,
      status: 'Completed', // In real implementation, this would be 'Processing' initially
      method,
      transactionId: `TXN${Date.now()}`,
      processedAt: new Date().toISOString(),
      createdAt: new Date().toISOString()
    };
    
    payments.push(newPayment);
    
    res.status(201).json({
      success: true,
      message: 'Payment processed successfully',
      data: newPayment
    });
    
  } catch (error) {
    console.error('Process payment error:', error);
    res.status(500).json({
      success: false,
      message: 'Server error'
    });
  }
});

// @route   POST /api/payments/:id/refund
// @desc    Process refund
// @access  Private
router.post('/:id/refund', [
  body('amount').optional().isFloat({ min: 0 }),
  body('reason').notEmpty()
], async (req, res) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        success: false,
        message: 'Validation errors',
        errors: errors.array()
      });
    }

    const paymentIndex = payments.findIndex(p => p.id === req.params.id);
    if (paymentIndex === -1) {
      return res.status(404).json({
        success: false,
        message: 'Payment not found'
      });
    }
    
    const { amount, reason } = req.body;
    const payment = payments[paymentIndex];
    
    if (payment.status !== 'Completed') {
      return res.status(400).json({
        success: false,
        message: 'Can only refund completed payments'
      });
    }
    
    const refundAmount = amount || payment.amount;
    
    // Mock refund processing
    const refund = {
      id: `REF${Date.now()}`,
      paymentId: payment.id,
      orderId: payment.orderId,
      amount: refundAmount,
      reason,
      status: 'Completed',
      processedAt: new Date().toISOString(),
      createdAt: new Date().toISOString()
    };
    
    // Update payment status
    payments[paymentIndex].status = 'Refunded';
    
    res.json({
      success: true,
      message: 'Refund processed successfully',
      data: refund
    });
    
  } catch (error) {
    console.error('Process refund error:', error);
    res.status(500).json({
      success: false,
      message: 'Server error'
    });
  }
});

// @route   GET /api/payments/status/:orderId/:paymentLinkId
// @desc    Get payment status for an order
// @access  Public (Guest checkout support)
router.get('/status/:orderId/:paymentLinkId', async (req, res) => {
  try {
    const { orderId, paymentLinkId } = req.params;

    const result = await paymentService.getOrderPaymentStatus(orderId, paymentLinkId);

    if (!result.success) {
      return res.status(400).json({
        success: false,
        message: result.error || 'Failed to get payment status',
        code: result.code
      });
    }

    res.json({
      success: true,
      data: result.data
    });

  } catch (error) {
    console.error('Get payment status error:', error);
    res.status(500).json({
      success: false,
      message: 'Server error while getting payment status'
    });
  }
});

// @route   POST /api/payments/cancel/:paymentLinkId
// @desc    Cancel/archive a payment link
// @access  Private
router.post('/cancel/:paymentLinkId', auth, async (req, res) => {
  try {
    const { paymentLinkId } = req.params;

    const result = await paymentService.cancelPaymentLink(paymentLinkId);

    if (!result.success) {
      return res.status(400).json({
        success: false,
        message: result.error || 'Failed to cancel payment link',
        code: result.code
      });
    }

    res.json({
      success: true,
      message: 'Payment link cancelled successfully',
      data: result.data
    });

  } catch (error) {
    console.error('Cancel payment link error:', error);
    res.status(500).json({
      success: false,
      message: 'Server error while cancelling payment link'
    });
  }
});

// @route   POST /api/payments/webhook
// @desc    PayMongo webhook endpoint for payment status updates
// @access  Public (but secured with signature validation)
router.post('/webhook', async (req, res) => {
  try {
    const webhookData = req.body;
    const signature = req.headers['paymongo-signature'];
    const rawBody = JSON.stringify(req.body);

    // Validate webhook signature for security
    const { paymongoClient } = require('../config/paymongo');
    const isValidSignature = paymongoClient.validateWebhookSignature(rawBody, signature);

    if (!isValidSignature) {
      console.error('❌ Invalid webhook signature');
      return res.status(401).json({
        success: false,
        message: 'Invalid webhook signature'
      });
    }

    // Process the webhook
    const result = await paymentService.processPaymentWebhook(webhookData);

    if (!result.success) {
      console.error('Webhook processing failed:', result.error);
      return res.status(400).json({
        success: false,
        message: result.error || 'Webhook processing failed'
      });
    }

    // If payment is completed, you might want to update order status here
    if (result.data.shouldUpdateOrder) {
      console.log(`✅ Payment completed for order ${result.data.orderId}`);
      // TODO: Integrate with order service to update order status
      // Example: await orderService.updateOrderStatus(result.data.orderId, 'paid');
    }

    res.json({
      success: true,
      message: 'Webhook processed successfully'
    });

  } catch (error) {
    console.error('❌ Webhook processing error:', error);
    res.status(500).json({
      success: false,
      message: 'Server error while processing webhook'
    });
  }
});

// @route   POST /api/payments/calculate-fees
// @desc    Calculate payment fees for display
// @access  Public (Guest checkout support)
router.post('/calculate-fees', [
  body('amount').isFloat({ min: 1 }).withMessage('Amount must be at least PHP 1.00'),
  body('paymentMethod').optional().isIn(['card', 'gcash', 'grabpay', 'bank']).withMessage('Invalid payment method')
], async (req, res) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        success: false,
        message: 'Validation errors',
        errors: errors.array()
      });
    }

    const { amount, paymentMethod = 'card' } = req.body;

    const feeCalculation = paymentService.calculatePaymentFees(amount, paymentMethod);

    res.json({
      success: true,
      data: feeCalculation
    });

  } catch (error) {
    console.error('Calculate fees error:', error);
    res.status(500).json({
      success: false,
      message: 'Server error while calculating fees'
    });
  }
});

module.exports = router;
