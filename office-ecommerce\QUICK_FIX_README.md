# 🚨 QUICK FIX: "Site Can't Be Reached" Error

If you're getting a "site can't be reached" error, follow these steps to get your site running immediately.

## 🔧 Quick Fix Options

### Option 1: Automated Fix (Recommended)
```bash
node fix-site-access.js
```
This script will:
- Install missing dependencies
- Fix port configurations
- Start both backend and frontend
- Test connectivity

### Option 2: Backend Only
```bash
node start-backend-only.js
```
This starts just the backend server on port 5001.

### Option 3: Manual Steps

#### Step 1: Install Dependencies
```bash
# Backend
cd backend
npm install

# Frontend
cd ../frontend
npm install
cd ..
```

#### Step 2: Start Backend
```bash
cd backend
npm start
```
Backend should run on: http://localhost:5001

#### Step 3: Start Frontend (in new terminal)
```bash
cd frontend
npm start
```
Frontend should run on: http://localhost:3000

## 🔍 Testing

### Test Backend
```bash
node test-backend.js
```

### Manual Testing
- Backend Health: http://localhost:5001/health
- Backend API: http://localhost:5001/api/health
- Frontend: http://localhost:3000

## 🐛 Common Issues & Solutions

### Issue 1: Port Already in Use
**Error:** `EADDRINUSE: address already in use :::5001`

**Solution:**
```bash
# Kill processes on port 5001
npx kill-port 5001

# Kill processes on port 3000
npx kill-port 3000
```

### Issue 2: Database Connection Errors
**Error:** `ELOGIN`, `ESOCKET`, `EINSTLOOKUP`

**Solution:** The quick fix scripts disable database connections and use mock data.

### Issue 3: Dependencies Missing
**Error:** `Cannot find module 'express'`

**Solution:**
```bash
cd backend && npm install
cd ../frontend && npm install
```

### Issue 4: Wrong Port Configuration
**Error:** Frontend can't connect to backend

**Solution:** The fix scripts ensure:
- Backend runs on port 5001
- Frontend connects to http://localhost:5001

## 📋 Verification Checklist

✅ Backend running on http://localhost:5001  
✅ Frontend running on http://localhost:3000  
✅ Health check responds: http://localhost:5001/health  
✅ API responds: http://localhost:5001/api/health  
✅ No console errors in browser  

## 🆘 If Nothing Works

1. **Check Node.js version:**
   ```bash
   node --version
   # Should be 16.0.0 or higher
   ```

2. **Clear npm cache:**
   ```bash
   npm cache clean --force
   ```

3. **Restart your computer** (sometimes helps with port issues)

4. **Check Windows Firewall** (may block Node.js)

5. **Try different ports:**
   - Change PORT=5001 to PORT=5002 in backend/.env
   - Change REACT_APP_API_URL=http://localhost:5002 in frontend/.env

## 📞 Quick Commands Reference

```bash
# Quick start everything
node fix-site-access.js

# Start backend only
node start-backend-only.js

# Test backend
node test-backend.js

# Kill all Node processes (if stuck)
taskkill /f /im node.exe

# Check what's running on ports
netstat -ano | findstr :5001
netstat -ano | findstr :3000
```

## 🎯 Expected Results

After running the fix:
- ✅ Backend API accessible at http://localhost:5001
- ✅ Frontend website accessible at http://localhost:3000
- ✅ No "site can't be reached" errors
- ✅ Basic functionality working (products, inventory, etc.)

## 💡 Notes

- The quick fix uses mock data (no database required)
- Payment processing may be limited without full database
- For full functionality, run the complete database setup later
- All fixes are temporary and safe (no data loss)

---

**Need more help?** Check the logs in `backend/logs/app.log` for detailed error information.
