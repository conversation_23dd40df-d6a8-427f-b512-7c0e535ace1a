const { connectDB } = require('./config/database');
const BaseModel = require('./models/BaseModel');

async function testDatabaseSchema() {
  try {
    console.log('🔍 Testing Database Schema...');

    // Connect to database
    console.log('\n📊 Connecting to database...');
    await connectDB();
    console.log('✅ Database connected');

    // Create base model instance to access database
    const baseModel = new BaseModel('Orders', 'OrderID');

    // Check if Orders table exists and get its columns
    console.log('\n🔍 Checking Orders table schema...');
    try {
      const ordersSchema = await baseModel.executeQuery(`
        SELECT COLUMN_NAME, DATA_TYPE, IS_NULLABLE, COLUMN_DEFAULT
        FROM INFORMATION_SCHEMA.COLUMNS 
        WHERE TABLE_NAME = 'Orders'
        ORDER BY ORDINAL_POSITION
      `);
      
      if (ordersSchema.length > 0) {
        console.log('✅ Orders table exists with columns:');
        ordersSchema.forEach(col => {
          console.log(`  - ${col.COLUMN_NAME} (${col.DATA_TYPE})`);
        });
      } else {
        console.log('❌ Orders table does not exist');
      }
    } catch (error) {
      console.log('❌ Error checking Orders table:', error.message);
    }

    // Check if OrderItems table exists and get its columns
    console.log('\n🔍 Checking OrderItems table schema...');
    try {
      const orderItemsSchema = await baseModel.executeQuery(`
        SELECT COLUMN_NAME, DATA_TYPE, IS_NULLABLE, COLUMN_DEFAULT
        FROM INFORMATION_SCHEMA.COLUMNS 
        WHERE TABLE_NAME = 'OrderItems'
        ORDER BY ORDINAL_POSITION
      `);
      
      if (orderItemsSchema.length > 0) {
        console.log('✅ OrderItems table exists with columns:');
        orderItemsSchema.forEach(col => {
          console.log(`  - ${col.COLUMN_NAME} (${col.DATA_TYPE})`);
        });
      } else {
        console.log('❌ OrderItems table does not exist');
      }
    } catch (error) {
      console.log('❌ Error checking OrderItems table:', error.message);
    }

    // Check if Users table exists
    console.log('\n🔍 Checking Users table schema...');
    try {
      const usersSchema = await baseModel.executeQuery(`
        SELECT COLUMN_NAME, DATA_TYPE, IS_NULLABLE, COLUMN_DEFAULT
        FROM INFORMATION_SCHEMA.COLUMNS 
        WHERE TABLE_NAME = 'Users'
        ORDER BY ORDINAL_POSITION
      `);
      
      if (usersSchema.length > 0) {
        console.log('✅ Users table exists with columns:');
        usersSchema.forEach(col => {
          console.log(`  - ${col.COLUMN_NAME} (${col.DATA_TYPE})`);
        });
      } else {
        console.log('❌ Users table does not exist');
      }
    } catch (error) {
      console.log('❌ Error checking Users table:', error.message);
    }

    console.log('\n✅ Schema check completed!');

  } catch (error) {
    console.error('❌ Test failed:', error.message);
    console.error('Stack:', error.stack);
  }
}

// Run the test
testDatabaseSchema();
