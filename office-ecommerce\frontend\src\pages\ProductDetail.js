import React, { useState, useEffect } from 'react';
import { usePara<PERSON>, Link } from 'react-router-dom';
import { getProductById } from '../services/products';
import { useCart } from '../contexts/CartContext';
import CheckoutModal from '../components/cart/CheckoutModal';
import Advanced3DConfigurator from '../components/3d/3DConfigurator';

const ProductDetail = () => {
    const { id } = useParams();
    const { addToCart } = useCart();
    const [product, setProduct] = useState(null);
    const [loading, setLoading] = useState(true);
    const [error, setError] = useState('');
    const [selectedImage, setSelectedImage] = useState(0);
    const [show3DConfigurator, setShow3DConfigurator] = useState(false);
    const [showCheckoutModal, setShowCheckoutModal] = useState(false);
    const [customization, setCustomization] = useState({
        color: '#6B7280',
        material: 'wood',
        dimensions: { width: 120, height: 75, depth: 60 }
    });
    const [quantity, setQuantity] = useState(1);

    useEffect(() => {
        loadProduct();
    }, [id]);

    const loadProduct = async () => {
        try {
            const response = await getProductById(id);
            setProduct(response.product);
        } catch (error) {
            console.error('Error loading product:', error);
            setError('Failed to load product details');
        } finally {
            setLoading(false);
        }
    };

    const formatPrice = (price) => {
        return new Intl.NumberFormat('en-US', {
            style: 'currency',
            currency: 'USD'
        }).format(price);
    };

    // Check if product supports advanced 3D configuration
    const supportsAdvanced3D = (product) => {
        if (!product) return false;
        const productType = product.name.toLowerCase();
        return productType.includes('table') ||
               productType.includes('desk') ||
               productType.includes('chair') ||
               productType.includes('cabinet') ||
               productType.includes('storage') ||
               productType.includes('workstation') ||
               productType.includes('shelf');
    };

    // Handle adding to cart with customization
    const handleAddToCart = () => {
        if (!product) return;

        try {
            addToCart(product, quantity, customization);
            setShowCheckoutModal(true);
        } catch (error) {
            console.error('Error adding to cart:', error);
            alert('Failed to add item to cart. Please try again.');
        }
    };

    if (loading) {
        return (
            <div className="product-detail-page">
                <div className="container">
                    <div className="loading">Loading product details...</div>
                </div>
            </div>
        );
    }

    if (error || !product) {
        return (
            <div className="product-detail-page">
                <div className="container">
                    <div className="error-message">
                        {error || 'Product not found'}
                        <Link to="/products" className="btn btn-primary">
                            Back to Products
                        </Link>
                    </div>
                </div>
            </div>
        );
    }

    const {
        name,
        description,
        price,
        discountPrice,
        images,
        categoryName,
        specifications
    } = product;

    const displayPrice = discountPrice || price;
    const hasDiscount = discountPrice && discountPrice < price;

    // Check if this product supports advanced 3D configuration
    const isConfigurable3D = supportsAdvanced3D(product);

    // If 3D configurator is enabled and it's a configurable product, show the configurator
    if (show3DConfigurator && isConfigurable3D) {
        return <Advanced3DConfigurator product={product} onBack={() => setShow3DConfigurator(false)} />;
    }

    return (
        <div className="product-detail-page">
            <div className="container">
                <div className="breadcrumb">
                    <Link to="/">Home</Link> /
                    <Link to="/products">Products</Link> /
                    <span>{name}</span>
                </div>

                <div className="product-detail">
                    <div className="product-media">
                        {/* Main Media Display */}
                        <div className="main-media">
                            <div className="main-image">
                                <img
                                    src={images && images[selectedImage] ? images[selectedImage] : 'https://images.unsplash.com/photo-1586023492125-27b2c045efd7?w=600'}
                                    alt={name}
                                    onError={(e) => {
                                        e.target.src = 'https://images.unsplash.com/photo-1586023492125-27b2c045efd7?w=600';
                                    }}
                                />
                                {isConfigurable3D && (
                                    <div className="image-overlay">
                                        <button
                                            className="configurator-btn"
                                            onClick={() => setShow3DConfigurator(true)}
                                        >
                                            <svg width="16" height="16" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                                                <path d="M12 2L2 7L12 12L22 7L12 2Z" fill="currentColor"/>
                                                <path d="M2 17L12 22L22 17" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
                                                <path d="M2 12L12 17L22 12" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
                                            </svg>
                                            3D Configurator
                                        </button>
                                    </div>
                                )}
                            </div>
                        </div>

                        {/* Thumbnails */}
                        <div className="media-thumbnails">
                            {images && images.length > 1 && (
                                <div className="image-thumbnails">
                                    {images.map((image, index) => (
                                        <img
                                            key={index}
                                            src={image}
                                            alt={`${name} ${index + 1}`}
                                            className={selectedImage === index ? 'active' : ''}
                                            onClick={() => setSelectedImage(index)}
                                            onError={(e) => {
                                                e.target.src = 'https://images.unsplash.com/photo-1586023492125-27b2c045efd7?w=150';
                                            }}
                                        />
                                    ))}
                                </div>
                            )}
                        </div>
                    </div>

                    <div className="product-info">
                        <div className="product-category">{categoryName}</div>
                        <h1>{name}</h1>

                        <div className="product-pricing">
                            <span className="current-price">{formatPrice(displayPrice)}</span>
                            {hasDiscount && (
                                <>
                                    <span className="original-price">{formatPrice(price)}</span>
                                    <span className="discount-badge">
                                        {Math.round(((price - discountPrice) / price) * 100)}% OFF
                                    </span>
                                </>
                            )}
                        </div>

                        <div className="product-description">
                            <p>{description}</p>
                        </div>

                        {specifications && Object.keys(specifications).length > 0 && (
                            <div className="product-specifications">
                                <h3>Specifications</h3>
                                <ul>
                                    {Object.entries(specifications).map(([key, value]) => (
                                        <li key={key}>
                                            <strong>{key.replace(/([A-Z])/g, ' $1').replace(/^./, str => str.toUpperCase())}:</strong> {value}
                                        </li>
                                    ))}
                                </ul>
                            </div>
                        )}

                        {/* Basic Customization Options */}
                        <div className="product-customization">
                            <h3>Product Options</h3>

                            <div className="customization-options">
                                <div className="option-group">
                                    <label>Quantity</label>
                                    <div className="quantity-control">
                                        <button
                                            onClick={() => setQuantity(Math.max(1, quantity - 1))}
                                            className="quantity-btn"
                                        >
                                            -
                                        </button>
                                        <span className="quantity-value">{quantity}</span>
                                        <button
                                            onClick={() => setQuantity(quantity + 1)}
                                            className="quantity-btn"
                                        >
                                            +
                                        </button>
                                    </div>
                                </div>

                                {isConfigurable3D && (
                                    <div className="option-group">
                                        <label>Advanced Customization</label>
                                        <p className="customization-note">
                                            Use the 3D Configurator to customize colors, materials, dimensions, and more advanced options.
                                        </p>
                                    </div>
                                )}
                            </div>
                        </div>

                        <div className="product-actions">
                            <button
                                className="btn btn-primary btn-large"
                                onClick={handleAddToCart}
                            >
                                Add to Cart - {formatPrice((discountPrice || price) * quantity)}
                            </button>
                            {isConfigurable3D && (
                                <button
                                    className="btn btn-3d-configurator btn-large"
                                    onClick={() => setShow3DConfigurator(true)}
                                >
                                    <svg width="20" height="20" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                                        <path d="M12 2L2 7L12 12L22 7L12 2Z" fill="currentColor"/>
                                        <path d="M2 17L12 22L22 17" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
                                        <path d="M2 12L12 17L22 12" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
                                    </svg>
                                    3D Configurator
                                </button>
                            )}
                        </div>
                    </div>
                </div>

                {/* Related Products Section */}
                <div className="related-products">
                    <h2>Related Products</h2>
                    <div className="related-products-grid">
                        {/* Placeholder for related products */}
                        <div className="related-product-card">
                            <div className="related-product-image">
                                <img src="https://images.unsplash.com/photo-1586023492125-27b2c045efd7?w=300" alt="Related Product" />
                            </div>
                            <div className="related-product-info">
                                <h4>Hotel Bed</h4>
                                <p>$2,299</p>
                            </div>
                        </div>
                        <div className="related-product-card">
                            <div className="related-product-image">
                                <img src="https://images.unsplash.com/photo-1586023492125-27b2c045efd7?w=300" alt="Related Product" />
                            </div>
                            <div className="related-product-info">
                                <h4>Conference Table</h4>
                                <p>$2,499</p>
                            </div>
                        </div>
                        <div className="related-product-card">
                            <div className="related-product-image">
                                <img src="https://images.unsplash.com/photo-1586023492125-27b2c045efd7?w=300" alt="Related Product" />
                            </div>
                            <div className="related-product-info">
                                <h4>Reception Desk</h4>
                                <p>$1,899</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            {/* Checkout Modal */}
            <CheckoutModal
                isOpen={showCheckoutModal}
                onClose={() => setShowCheckoutModal(false)}
                product={product}
                quantity={quantity}
            />
        </div>
    );
};

export default ProductDetail;
