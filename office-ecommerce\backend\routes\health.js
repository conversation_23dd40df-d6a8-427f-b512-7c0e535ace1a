const express = require('express');
const { getPool } = require('../config/database');
const logger = require('../utils/logger');
const router = express.Router();

/**
 * Comprehensive Health Check Routes
 * Provides detailed system status information
 */

// Basic health check
router.get('/', async (req, res) => {
  try {
    const healthStatus = {
      status: 'OK',
      timestamp: new Date().toISOString(),
      uptime: process.uptime(),
      environment: process.env.NODE_ENV || 'development',
      version: '1.0.0',
      services: {
        api: 'healthy',
        database: 'unknown',
        websocket: 'healthy',
        payment: 'unknown'
      }
    };

    // Check database connection
    try {
      const pool = getPool();
      await pool.request().query('SELECT 1 as test');
      healthStatus.services.database = 'healthy';
    } catch (error) {
      healthStatus.services.database = 'unhealthy';
      healthStatus.status = 'DEGRADED';
      logger.warn('Database health check failed:', error.message);
    }

    // Check PayMongo configuration
    if (process.env.PAYMONGO_SECRET_KEY && process.env.PAYMONGO_PUBLIC_KEY) {
      healthStatus.services.payment = 'configured';
    } else {
      healthStatus.services.payment = 'not_configured';
    }

    res.status(healthStatus.status === 'OK' ? 200 : 503).json(healthStatus);

  } catch (error) {
    logger.error('Health check error:', error);
    res.status(500).json({
      status: 'ERROR',
      timestamp: new Date().toISOString(),
      error: 'Health check failed'
    });
  }
});

// Detailed system information (admin only)
router.get('/detailed', async (req, res) => {
  try {
    const systemInfo = {
      status: 'OK',
      timestamp: new Date().toISOString(),
      system: {
        nodeVersion: process.version,
        platform: process.platform,
        arch: process.arch,
        uptime: process.uptime(),
        memory: process.memoryUsage(),
        pid: process.pid
      },
      environment: {
        nodeEnv: process.env.NODE_ENV,
        port: process.env.PORT,
        logLevel: process.env.LOG_LEVEL
      },
      services: {
        api: { status: 'healthy', port: process.env.PORT || 5001 },
        database: { status: 'unknown', server: process.env.DB_SERVER },
        websocket: { status: 'healthy', enabled: true },
        payment: { 
          status: 'unknown', 
          configured: !!(process.env.PAYMONGO_SECRET_KEY && process.env.PAYMONGO_PUBLIC_KEY)
        }
      },
      features: {
        authentication: true,
        inventory: true,
        orders: true,
        payments: !!(process.env.PAYMONGO_SECRET_KEY),
        websockets: true,
        fileUpload: true
      }
    };

    // Test database connection with more details
    try {
      const pool = getPool();
      const startTime = Date.now();
      await pool.request().query('SELECT 1 as test');
      const responseTime = Date.now() - startTime;
      
      systemInfo.services.database = {
        status: 'healthy',
        server: process.env.DB_SERVER,
        database: process.env.DB_NAME,
        responseTime: `${responseTime}ms`
      };
    } catch (error) {
      systemInfo.services.database = {
        status: 'unhealthy',
        server: process.env.DB_SERVER,
        error: error.message
      };
      systemInfo.status = 'DEGRADED';
    }

    // Test PayMongo configuration
    if (process.env.PAYMONGO_SECRET_KEY && process.env.PAYMONGO_PUBLIC_KEY) {
      systemInfo.services.payment.status = 'configured';
      systemInfo.services.payment.webhookConfigured = !!process.env.PAYMONGO_WEBHOOK_SECRET;
    } else {
      systemInfo.services.payment.status = 'not_configured';
      systemInfo.features.payments = false;
    }

    res.status(systemInfo.status === 'OK' ? 200 : 503).json(systemInfo);

  } catch (error) {
    logger.error('Detailed health check error:', error);
    res.status(500).json({
      status: 'ERROR',
      timestamp: new Date().toISOString(),
      error: 'Detailed health check failed'
    });
  }
});

// Database-specific health check
router.get('/database', async (req, res) => {
  try {
    const dbHealth = {
      status: 'unknown',
      timestamp: new Date().toISOString(),
      database: {
        server: process.env.DB_SERVER,
        name: process.env.DB_NAME,
        user: process.env.DB_USER
      }
    };

    const pool = getPool();
    const startTime = Date.now();
    
    // Test basic connection
    await pool.request().query('SELECT 1 as test');
    const responseTime = Date.now() - startTime;
    
    // Get database info
    const dbInfo = await pool.request().query(`
      SELECT 
        @@VERSION as version,
        DB_NAME() as current_database,
        @@SERVERNAME as server_name,
        GETDATE() as server_time
    `);

    dbHealth.status = 'healthy';
    dbHealth.responseTime = `${responseTime}ms`;
    dbHealth.database.version = dbInfo.recordset[0].version.split('\n')[0];
    dbHealth.database.serverName = dbInfo.recordset[0].server_name;
    dbHealth.database.serverTime = dbInfo.recordset[0].server_time;

    res.json(dbHealth);

  } catch (error) {
    logger.error('Database health check error:', error);
    res.status(503).json({
      status: 'unhealthy',
      timestamp: new Date().toISOString(),
      error: error.message,
      database: {
        server: process.env.DB_SERVER,
        name: process.env.DB_NAME
      }
    });
  }
});

// API endpoints health check
router.get('/endpoints', async (req, res) => {
  try {
    const endpoints = [
      { path: '/api/auth', description: 'Authentication endpoints' },
      { path: '/api/products', description: 'Product management' },
      { path: '/api/inventory', description: 'Inventory management' },
      { path: '/api/orders', description: 'Order management' },
      { path: '/api/suppliers', description: 'Supplier management' },
      { path: '/api/admin', description: 'Admin functions' },
      { path: '/api/payments', description: 'Payment processing' }
    ];

    const endpointHealth = {
      status: 'OK',
      timestamp: new Date().toISOString(),
      endpoints: endpoints.map(endpoint => ({
        ...endpoint,
        status: 'available'
      }))
    };

    res.json(endpointHealth);

  } catch (error) {
    logger.error('Endpoints health check error:', error);
    res.status(500).json({
      status: 'ERROR',
      timestamp: new Date().toISOString(),
      error: 'Endpoints health check failed'
    });
  }
});

// Ready check (for Kubernetes/Docker)
router.get('/ready', async (req, res) => {
  try {
    // Check if all critical services are ready
    const pool = getPool();
    await pool.request().query('SELECT 1 as test');
    
    res.json({
      status: 'READY',
      timestamp: new Date().toISOString(),
      message: 'Application is ready to serve requests'
    });

  } catch (error) {
    logger.error('Readiness check error:', error);
    res.status(503).json({
      status: 'NOT_READY',
      timestamp: new Date().toISOString(),
      error: 'Application is not ready'
    });
  }
});

// Liveness check (for Kubernetes/Docker)
router.get('/live', (req, res) => {
  res.json({
    status: 'ALIVE',
    timestamp: new Date().toISOString(),
    uptime: process.uptime(),
    message: 'Application is alive'
  });
});

module.exports = router;
