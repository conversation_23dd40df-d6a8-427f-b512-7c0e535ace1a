/* Enhanced Admin Components Styling */
/* Consistent with frontend design system and golden yellow (#F0B21B) accent color */

/* Metric Cards */
.metrics-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
  gap: 1.5rem;
  margin-bottom: 2rem;
}

.metric-card {
  background: white;
  border-radius: 16px;
  padding: 1.5rem;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
  border: 1px solid rgba(0, 0, 0, 0.05);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  position: relative;
  overflow: hidden;
}

.metric-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 4px;
  background: linear-gradient(90deg, #F0B21B, #e6a632);
}

.metric-card:hover {
  transform: translateY(-4px);
  box-shadow: 0 8px 30px rgba(0, 0, 0, 0.12);
}

.metric-card.alert::before {
  background: linear-gradient(90deg, #F59E0B, #D97706);
}

.metric-card.pending::before {
  background: linear-gradient(90deg, #6B7280, #4B5563);
}

.metric-card {
  display: flex;
  align-items: center;
  gap: 1rem;
}

.metric-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 60px;
  height: 60px;
  background: rgba(240, 178, 27, 0.1);
  border-radius: 12px;
  flex-shrink: 0;
}

.metric-content h3 {
  font-size: 2rem;
  font-weight: 700;
  color: #1F2937;
  margin: 0 0 0.25rem 0;
  line-height: 1.2;
}

.metric-content p {
  font-size: 0.875rem;
  color: #6B7280;
  margin: 0;
  font-weight: 500;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

/* Admin Cards */
.admin-card {
  background: white;
  border-radius: 16px;
  padding: 1.5rem;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
  border: 1px solid rgba(0, 0, 0, 0.05);
  margin-bottom: 1.5rem;
  transition: all 0.3s ease;
}

.admin-card:hover {
  box-shadow: 0 8px 30px rgba(0, 0, 0, 0.12);
}

.admin-card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1.5rem;
  padding-bottom: 1rem;
  border-bottom: 2px solid #F3F4F6;
}

.admin-card-title {
  font-size: 1.25rem;
  font-weight: 600;
  color: #1F2937;
  margin: 0;
  letter-spacing: 0.3px;
}

/* Buttons */
.admin-btn {
  padding: 0.75rem 1.5rem;
  border: none;
  border-radius: 8px;
  font-size: 0.875rem;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  text-decoration: none;
  display: inline-flex;
  align-items: center;
  gap: 0.5rem;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.admin-btn-primary {
  background: #F0B21B;
  color: white;
  box-shadow: 0 2px 8px rgba(240, 178, 27, 0.3);
}

.admin-btn-primary:hover {
  background: #e6a632;
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(240, 178, 27, 0.4);
}

.admin-btn-secondary {
  background: #F3F4F6;
  color: #374151;
  border: 2px solid #E5E7EB;
}

.admin-btn-secondary:hover {
  background: #E5E7EB;
  border-color: #F0B21B;
  color: #F0B21B;
  transform: translateY(-2px);
}

.admin-btn-danger {
  background: #EF4444;
  color: white;
  box-shadow: 0 2px 8px rgba(239, 68, 68, 0.3);
}

.admin-btn-danger:hover {
  background: #DC2626;
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(239, 68, 68, 0.4);
}

/* Tables */
.admin-table {
  width: 100%;
  border-collapse: collapse;
  background: white;
  border-radius: 12px;
  overflow: hidden;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
}

.admin-table th {
  background: #F9FAFB;
  padding: 1rem;
  text-align: left;
  font-weight: 600;
  color: #374151;
  font-size: 0.875rem;
  text-transform: uppercase;
  letter-spacing: 0.5px;
  border-bottom: 2px solid #E5E7EB;
}

.admin-table td {
  padding: 1rem;
  border-bottom: 1px solid #F3F4F6;
  color: #6B7280;
  font-size: 0.875rem;
}

.admin-table tr:hover {
  background: #F9FAFB;
}

.admin-table tr:last-child td {
  border-bottom: none;
}

/* Status Badges */
.status-badge {
  padding: 0.375rem 0.75rem;
  border-radius: 20px;
  font-size: 0.75rem;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.5px;
  color: white;
  display: inline-block;
}

/* Form Elements */
.admin-form-group {
  margin-bottom: 1.5rem;
}

.admin-label {
  display: block;
  font-size: 0.875rem;
  font-weight: 600;
  color: #374151;
  margin-bottom: 0.5rem;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.admin-input,
.admin-select,
.admin-textarea {
  width: 100%;
  padding: 0.75rem 1rem;
  border: 2px solid #E5E7EB;
  border-radius: 8px;
  font-size: 0.875rem;
  color: #374151;
  background: white;
  transition: all 0.3s ease;
}

.admin-input:focus,
.admin-select:focus,
.admin-textarea:focus {
  outline: none;
  border-color: #F0B21B;
  box-shadow: 0 0 0 3px rgba(240, 178, 27, 0.1);
}

/* Loading States */
.admin-loading {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 3rem;
  color: #6B7280;
}

.loading-spinner {
  width: 40px;
  height: 40px;
  border: 4px solid #F3F4F6;
  border-top: 4px solid #F0B21B;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 1rem;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* Alert Components */
.alert-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 1rem;
  background: #FEF3C7;
  border: 1px solid #F59E0B;
  border-radius: 8px;
  margin-bottom: 0.75rem;
}

.alert-info h4 {
  font-size: 0.875rem;
  font-weight: 600;
  color: #92400E;
  margin: 0 0 0.25rem 0;
}

.alert-info p {
  font-size: 0.75rem;
  color: #B45309;
  margin: 0;
}

/* Responsive Design for Admin Components */
@media (max-width: 768px) {
  .metrics-grid {
    grid-template-columns: 1fr;
    gap: 1rem;
  }

  .metric-card {
    padding: 1rem;
  }

  .metric-icon {
    width: 50px;
    height: 50px;
  }

  .metric-content h3 {
    font-size: 1.5rem;
  }

  .admin-card {
    padding: 1rem;
    margin-bottom: 1rem;
  }

  .admin-card-header {
    flex-direction: column;
    gap: 1rem;
    align-items: stretch;
  }

  .admin-btn {
    width: 100%;
    justify-content: center;
  }

  .admin-table {
    font-size: 0.8rem;
  }

  .admin-table th,
  .admin-table td {
    padding: 0.75rem 0.5rem;
  }
}

@media (max-width: 480px) {
  .metric-card {
    flex-direction: column;
    text-align: center;
    gap: 0.75rem;
  }

  .metric-content h3 {
    font-size: 1.25rem;
  }

  .admin-table th,
  .admin-table td {
    padding: 0.5rem 0.25rem;
    font-size: 0.75rem;
  }
}

/* Analytics Charts Styles */
.analytics {
  padding: 2rem;
}

/* Charts Grid */
.charts-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
  gap: 1.5rem;
  margin-bottom: 2rem;
}

.chart-card {
  background: white;
  border-radius: 16px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
  border: 1px solid rgba(0, 0, 0, 0.05);
  overflow: hidden;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  position: relative;
}

.chart-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 4px;
  background: linear-gradient(90deg, #F0B21B, #e6a632);
}

.chart-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 30px rgba(0, 0, 0, 0.12);
}

.chart-header {
  padding: 1.5rem 1.5rem 1rem 1.5rem;
  border-bottom: 1px solid #f3f4f6;
  background: linear-gradient(135deg, #f8fafc 0%, #ffffff 100%);
}

.chart-header h3 {
  margin: 0 0 0.25rem 0;
  font-size: 1.125rem;
  font-weight: 600;
  color: #1f2937;
}

.chart-subtitle {
  font-size: 0.875rem;
  color: #6b7280;
  font-weight: 400;
}

.chart-container {
  padding: 1.5rem;
  height: 300px;
  position: relative;
}

/* Analytics Content */
.analytics-content {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.analytics-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0.75rem 0;
  border-bottom: 1px solid #f3f4f6;
}

.analytics-item:last-child {
  border-bottom: none;
}

.analytics-item span:first-child {
  font-weight: 500;
  color: #374151;
}

.analytics-item span:last-child {
  font-weight: 600;
  color: #1f2937;
}

.analytics-item .warning {
  color: #f59e0b;
}

.analytics-item .danger {
  color: #ef4444;
}

/* Chart Responsive Styles */
@media (max-width: 1200px) {
  .charts-grid {
    grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
  }
}

@media (max-width: 768px) {
  .charts-grid {
    grid-template-columns: 1fr;
    gap: 1rem;
  }

  .chart-container {
    height: 250px;
    padding: 1rem;
  }

  .chart-header {
    padding: 1rem;
  }

  .analytics {
    padding: 1rem;
  }

  .metrics-grid {
    grid-template-columns: 1fr;
    gap: 1rem;
  }
}

@media (max-width: 480px) {
  .chart-container {
    height: 200px;
    padding: 0.75rem;
  }

  .chart-header {
    padding: 0.75rem;
  }

  .chart-header h3 {
    font-size: 1rem;
  }

  .chart-subtitle {
    font-size: 0.75rem;
  }
}

/* Order Management Specific Styles */
.filters-section {
  background: #F9FAFB;
  border-radius: 12px;
  padding: 1.5rem;
  border: 1px solid #E5E7EB;
}

.filters-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 1rem;
}

.filter-group {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.filter-group label {
  font-size: 0.875rem;
  font-weight: 600;
  color: #374151;
}

.search-input-container {
  position: relative;
  display: flex;
  align-items: center;
}

.search-input-container svg {
  position: absolute;
  left: 12px;
  color: #6B7280;
  z-index: 1;
}

.search-input {
  width: 100%;
  padding: 0.75rem 0.75rem 0.75rem 2.5rem;
  border: 1px solid #D1D5DB;
  border-radius: 8px;
  font-size: 0.875rem;
  transition: all 0.2s ease;
}

.search-input:focus {
  outline: none;
  border-color: #F0B21B;
  box-shadow: 0 0 0 3px rgba(240, 178, 27, 0.1);
}

.filter-select,
.date-input {
  padding: 0.75rem;
  border: 1px solid #D1D5DB;
  border-radius: 8px;
  font-size: 0.875rem;
  background: white;
  transition: all 0.2s ease;
}

.filter-select:focus,
.date-input:focus {
  outline: none;
  border-color: #F0B21B;
  box-shadow: 0 0 0 3px rgba(240, 178, 27, 0.1);
}

.date-range-inputs {
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.date-range-inputs span {
  font-size: 0.875rem;
  color: #6B7280;
  font-weight: 500;
}

.item-count-badge {
  background: rgba(240, 178, 27, 0.1);
  color: #F0B21B;
  padding: 0.25rem 0.5rem;
  border-radius: 6px;
  font-size: 0.75rem;
  font-weight: 600;
}

.pagination-container {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-top: 1.5rem;
  padding-top: 1.5rem;
  border-top: 1px solid #E5E7EB;
}

.pagination-info {
  font-size: 0.875rem;
  color: #6B7280;
}

.pagination-controls {
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.page-numbers {
  display: flex;
  gap: 0.25rem;
}

/* Modal Styles */
.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
  padding: 1rem;
}

.modal-content {
  background: white;
  border-radius: 16px;
  box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
  max-width: 500px;
  width: 100%;
  max-height: 90vh;
  overflow-y: auto;
}

.modal-content.large {
  max-width: 800px;
}

.modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 1.5rem;
  border-bottom: 1px solid #E5E7EB;
}

.modal-header h3 {
  margin: 0;
  font-size: 1.25rem;
  font-weight: 700;
  color: #1F2937;
}

.modal-close {
  background: none;
  border: none;
  font-size: 1.5rem;
  color: #6B7280;
  cursor: pointer;
  padding: 0;
  width: 32px;
  height: 32px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 6px;
  transition: all 0.2s ease;
}

.modal-close:hover {
  background: #F3F4F6;
  color: #374151;
}

.modal-body {
  padding: 1.5rem;
}

.modal-footer {
  display: flex;
  justify-content: flex-end;
  gap: 0.75rem;
  padding: 1.5rem;
  border-top: 1px solid #E5E7EB;
}

/* Order Details Modal Styles */
.order-details-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 1.5rem;
}

.detail-section {
  background: #F9FAFB;
  border-radius: 12px;
  padding: 1.25rem;
  border: 1px solid #E5E7EB;
}

.detail-section.full-width {
  grid-column: 1 / -1;
}

.detail-section h4 {
  margin: 0 0 1rem 0;
  font-size: 1rem;
  font-weight: 700;
  color: #1F2937;
  border-bottom: 2px solid #F0B21B;
  padding-bottom: 0.5rem;
}

.detail-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 0.75rem;
}

.detail-item:last-child {
  margin-bottom: 0;
}

.detail-item label {
  font-weight: 600;
  color: #374151;
  font-size: 0.875rem;
}

.detail-item span {
  color: #6B7280;
  font-size: 0.875rem;
}

.order-items-table {
  width: 100%;
  border-collapse: collapse;
  margin-top: 0.75rem;
}

.order-items-table th,
.order-items-table td {
  padding: 0.75rem;
  text-align: left;
  border-bottom: 1px solid #E5E7EB;
}

.order-items-table th {
  background: #F3F4F6;
  font-weight: 600;
  color: #374151;
  font-size: 0.875rem;
}

.order-items-table td {
  font-size: 0.875rem;
  color: #6B7280;
}

.order-totals {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.total-line {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0.5rem 0;
  font-size: 0.875rem;
}

.total-line.discount {
  color: #10B981;
}

.total-line.total {
  border-top: 2px solid #E5E7EB;
  margin-top: 0.5rem;
  padding-top: 0.75rem;
  font-weight: 700;
  font-size: 1rem;
  color: #1F2937;
}

/* Form Styles */
.form-group {
  margin-bottom: 1.25rem;
}

.form-group label {
  display: block;
  margin-bottom: 0.5rem;
  font-weight: 600;
  color: #374151;
  font-size: 0.875rem;
}

.form-group input,
.form-group select,
.form-group textarea {
  width: 100%;
  padding: 0.75rem;
  border: 1px solid #D1D5DB;
  border-radius: 8px;
  font-size: 0.875rem;
  transition: all 0.2s ease;
}

.form-group input:focus,
.form-group select:focus,
.form-group textarea:focus {
  outline: none;
  border-color: #F0B21B;
  box-shadow: 0 0 0 3px rgba(240, 178, 27, 0.1);
}

.form-group input:disabled {
  background: #F3F4F6;
  color: #6B7280;
  cursor: not-allowed;
}

.form-group textarea {
  resize: vertical;
  min-height: 80px;
}

/* Order Management Responsive Design */
@media (max-width: 768px) {
  .filters-grid {
    grid-template-columns: 1fr;
  }

  .date-range-inputs {
    flex-direction: column;
    align-items: stretch;
  }

  .pagination-container {
    flex-direction: column;
    gap: 1rem;
    align-items: stretch;
  }

  .pagination-controls {
    justify-content: center;
  }

  .modal-content {
    margin: 1rem;
    max-width: none;
  }

  .order-details-grid {
    grid-template-columns: 1fr;
  }

  .modal-footer {
    flex-direction: column;
  }

  .modal-footer .btn {
    width: 100%;
  }

  .admin-table th,
  .admin-table td {
    padding: 0.5rem;
    font-size: 0.875rem;
  }

  .action-buttons {
    flex-direction: column;
    gap: 0.25rem;
  }

  .action-buttons .btn {
    width: 100%;
    justify-content: center;
  }
}
