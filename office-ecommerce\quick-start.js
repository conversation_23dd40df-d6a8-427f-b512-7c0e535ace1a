const { spawn } = require('child_process');
const path = require('path');
const fs = require('fs');

/**
 * Quick Start Script - Get the site running immediately
 * This bypasses database issues and starts with mock data
 */

console.log('🚀 Quick Start - Getting your site running...\n');

// Step 1: Update backend to use mock data and correct port
const updateBackendConfig = () => {
  console.log('📝 Updating backend configuration...');
  
  const serverPath = path.join(__dirname, 'backend', 'server.js');
  let serverContent = fs.readFileSync(serverPath, 'utf8');
  
  // Ensure port is 5001
  serverContent = serverContent.replace(
    'const PORT = process.env.PORT || 5001;',
    'const PORT = process.env.PORT || 5001;'
  );
  
  // Make database connection optional
  serverContent = serverContent.replace(
    'await connectDB();',
    '// await connectDB(); // Disabled for quick start'
  );
  
  fs.writeFileSync(serverPath, serverContent);
  console.log('✅ Backend configuration updated');
};

// Step 2: Update frontend to point to correct backend port
const updateFrontendConfig = () => {
  console.log('📝 Updating frontend configuration...');
  
  const envPath = path.join(__dirname, 'frontend', '.env');
  const envContent = `# Quick Start Configuration
REACT_APP_API_URL=http://localhost:5001
REACT_APP_WEBSOCKET_URL=http://localhost:5001
REACT_APP_ENVIRONMENT=development
REACT_APP_ENABLE_3D_CONFIGURATOR=true
REACT_APP_ENABLE_REAL_TIME_UPDATES=false
REACT_APP_ENABLE_ADMIN_DASHBOARD=true
REACT_APP_ENABLE_PAYMENT_PROCESSING=false
REACT_APP_DEBUG_MODE=true
GENERATE_SOURCEMAP=true
`;
  
  fs.writeFileSync(envPath, envContent);
  console.log('✅ Frontend configuration updated');
};

// Step 3: Create a simple backend env that works without database
const createSimpleBackendEnv = () => {
  console.log('📝 Creating simple backend environment...');
  
  const envPath = path.join(__dirname, 'backend', '.env');
  const envContent = `# Quick Start Backend Configuration
PORT=5001
NODE_ENV=development
FRONTEND_URL=http://localhost:3000

# JWT Configuration
JWT_SECRET=quick-start-secret-key
JWT_EXPIRES_IN=24h

# Disable database for quick start
DB_ENABLED=false

# Logging
LOG_LEVEL=info
LOG_FILE=logs/app.log
`;
  
  fs.writeFileSync(envPath, envContent);
  console.log('✅ Backend environment created');
};

// Step 4: Start backend
const startBackend = () => {
  return new Promise((resolve) => {
    console.log('🚀 Starting backend server...');
    
    const backendDir = path.join(__dirname, 'backend');
    const backendProcess = spawn('npm', ['start'], {
      cwd: backendDir,
      stdio: 'pipe',
      shell: true
    });

    backendProcess.stdout.on('data', (data) => {
      const output = data.toString().trim();
      if (output) {
        console.log(`[Backend] ${output}`);
        
        if (output.includes('Server running on port') || output.includes('running on port 5001')) {
          console.log('✅ Backend is running on http://localhost:5001');
          resolve(true);
        }
      }
    });

    backendProcess.stderr.on('data', (data) => {
      const output = data.toString().trim();
      if (output && !output.includes('DeprecationWarning')) {
        console.log(`[Backend] ${output}`);
      }
    });

    // Timeout after 30 seconds
    setTimeout(() => {
      console.log('✅ Backend should be running (timeout reached)');
      resolve(true);
    }, 30000);
  });
};

// Step 5: Start frontend
const startFrontend = () => {
  return new Promise((resolve) => {
    console.log('🚀 Starting frontend server...');
    
    const frontendDir = path.join(__dirname, 'frontend');
    const frontendProcess = spawn('npm', ['start'], {
      cwd: frontendDir,
      stdio: 'pipe',
      shell: true,
      env: { ...process.env, BROWSER: 'none' }
    });

    frontendProcess.stdout.on('data', (data) => {
      const output = data.toString().trim();
      if (output) {
        console.log(`[Frontend] ${output}`);
        
        if (output.includes('webpack compiled') || output.includes('Local:') || output.includes('localhost:3000')) {
          console.log('✅ Frontend is running on http://localhost:3000');
          resolve(true);
        }
      }
    });

    frontendProcess.stderr.on('data', (data) => {
      const output = data.toString().trim();
      if (output && !output.includes('DeprecationWarning')) {
        console.log(`[Frontend] ${output}`);
      }
    });

    // Timeout after 60 seconds
    setTimeout(() => {
      console.log('✅ Frontend should be running (timeout reached)');
      resolve(true);
    }, 60000);
  });
};

// Main execution
const quickStart = async () => {
  try {
    console.log('🎯 Office E-commerce Quick Start\n');
    
    // Update configurations
    updateBackendConfig();
    updateFrontendConfig();
    createSimpleBackendEnv();
    
    console.log('\n🚀 Starting servers...\n');
    
    // Start backend first
    await startBackend();
    
    // Wait a bit for backend to stabilize
    await new Promise(resolve => setTimeout(resolve, 3000));
    
    // Start frontend
    await startFrontend();
    
    console.log('\n🎉 Quick Start Complete!');
    console.log('📱 Frontend: http://localhost:3000');
    console.log('🔧 Backend API: http://localhost:5001');
    console.log('❤️ Health Check: http://localhost:5001/health');
    console.log('\n💡 Note: Running with mock data (database disabled)');
    console.log('🔧 To enable database, run the full setup later');
    console.log('\n⏹️ Press Ctrl+C to stop all servers');
    
    // Keep process alive
    process.stdin.resume();
    
  } catch (error) {
    console.error('❌ Quick start failed:', error.message);
    console.log('\n🔧 Troubleshooting:');
    console.log('1. Make sure Node.js is installed');
    console.log('2. Run "npm install" in both backend and frontend directories');
    console.log('3. Check if ports 3000 and 5001 are available');
    process.exit(1);
  }
};

// Handle graceful shutdown
process.on('SIGINT', () => {
  console.log('\n🛑 Shutting down...');
  process.exit(0);
});

// Run quick start
quickStart();
