const axios = require('axios');

async function testActivityEndpoint() {
  try {
    console.log('🔍 Testing Activity Logs Endpoints...');

    // First, login to get a valid token
    console.log('\n🔐 Logging in to get authentication token...');
    let authToken = null;
    try {
      const loginResponse = await axios.post('http://localhost:8000/api/auth/login', {
        email: '<EMAIL>',
        password: 'admin123'
      });
      authToken = loginResponse.data.data.token;
      console.log('✅ Login successful, got token');
    } catch (error) {
      console.log('❌ Login failed:', error.response?.status, error.response?.data?.message || error.message);
      return;
    }

    // Test the specific endpoint that was failing
    console.log('\n🔍 Testing /api/admin/activity-logs/actions endpoint...');
    try {
      const response = await axios.get('http://localhost:8000/api/admin/activity-logs/actions', {
        headers: {
          'Authorization': `Bear<PERSON> ${authToken}`
        }
      });
      console.log('✅ Activity logs actions endpoint working');
      console.log('Response:', JSON.stringify(response.data, null, 2));
    } catch (error) {
      console.log('❌ Activity logs actions endpoint failed:', error.response?.status, error.response?.data?.message || error.message);
    }

    // Test other activity log endpoints
    console.log('\n🔍 Testing /api/admin/activity-logs endpoint...');
    try {
      const response = await axios.get('http://localhost:8000/api/admin/activity-logs', {
        headers: {
          'Authorization': `Bearer ${authToken}`
        }
      });
      console.log('✅ Activity logs endpoint working');
      console.log('Response:', JSON.stringify(response.data, null, 2));
    } catch (error) {
      console.log('❌ Activity logs endpoint failed:', error.response?.status, error.response?.data?.message || error.message);
    }

    console.log('\n✅ Test completed!');

  } catch (error) {
    console.error('❌ Test failed:', error.message);
  }
}

// Run the test
testActivityEndpoint();
