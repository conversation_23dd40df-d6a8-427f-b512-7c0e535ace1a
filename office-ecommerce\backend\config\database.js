const sql = require('mssql');
const logger = require('../utils/logger');

// Database configuration - Use SQL Server Authentication
const dbConfig = {
  server: process.env.DB_SERVER || 'DESKTOP-DPNS718\\SQLEXPRESS',
  database: process.env.DB_NAME || 'OfficeEcommerce',
  user: process.env.DB_USER || 'OfficeEcommerceUser',
  password: process.env.DB_PASSWORD || 'OfficeApp123!',
  port: parseInt(process.env.DB_PORT) || 1433,
  options: {
    encrypt: process.env.DB_ENCRYPT === 'true' || false,
    trustServerCertificate: process.env.DB_TRUST_CERT === 'true' || true,
    enableArithAbort: true,
    requestTimeout: 30000,
    connectionTimeout: 30000,
    instanceName: 'SQLEXPRESS', // Add instance name for SQL Server Express
    pool: {
      max: 10,
      min: 0,
      idleTimeoutMillis: 30000
    }
  }
};

let pool = null;

// Connect to database
const connectDB = async () => {
  try {
    if (pool) {
      return pool;
    }

    logger.info('Connecting to MSSQL database...');
    pool = await sql.connect(dbConfig);
    
    logger.info('Database connected successfully');
    
    // Test the connection
    const result = await pool.request().query('SELECT 1 as test');
    logger.info('Database connection test successful');
    
    return pool;
  } catch (error) {
    logger.error('Database connection failed:', error);
    throw error;
  }
};

// Get database pool
const getPool = () => {
  if (!pool) {
    throw new Error('Database not connected. Call connectDB() first.');
  }
  return pool;
};

// Close database connection
const closeDB = async () => {
  try {
    if (pool) {
      await pool.close();
      pool = null;
      logger.info('Database connection closed');
    }
  } catch (error) {
    logger.error('Error closing database connection:', error);
    throw error;
  }
};

// Execute query with error handling
const executeQuery = async (query, params = {}) => {
  try {
    const pool = getPool();
    const request = pool.request();
    
    // Add parameters to request
    Object.keys(params).forEach(key => {
      request.input(key, params[key]);
    });
    
    const result = await request.query(query);
    return result;
  } catch (error) {
    logger.error('Query execution failed:', error);
    throw error;
  }
};

// Execute stored procedure
const executeStoredProcedure = async (procedureName, params = {}) => {
  try {
    const pool = getPool();
    const request = pool.request();
    
    // Add parameters to request
    Object.keys(params).forEach(key => {
      request.input(key, params[key]);
    });
    
    const result = await request.execute(procedureName);
    return result;
  } catch (error) {
    logger.error('Stored procedure execution failed:', error);
    throw error;
  }
};

// Transaction helper
const executeTransaction = async (operations) => {
  const pool = getPool();
  const transaction = new sql.Transaction(pool);
  
  try {
    await transaction.begin();
    
    const results = [];
    for (const operation of operations) {
      const request = new sql.Request(transaction);
      
      // Add parameters if provided
      if (operation.params) {
        Object.keys(operation.params).forEach(key => {
          request.input(key, operation.params[key]);
        });
      }
      
      const result = await request.query(operation.query);
      results.push(result);
    }
    
    await transaction.commit();
    return results;
  } catch (error) {
    await transaction.rollback();
    logger.error('Transaction failed:', error);
    throw error;
  }
};

module.exports = {
  connectDB,
  getPool,
  closeDB,
  executeQuery,
  executeStoredProcedure,
  executeTransaction,
  sql
};
