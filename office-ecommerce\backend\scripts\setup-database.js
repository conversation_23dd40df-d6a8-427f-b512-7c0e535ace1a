const sql = require('mssql');
const logger = require('../utils/logger');

// Database setup script for SQL Server Express
const setupDatabase = async () => {
  try {
    logger.info('Starting database setup...');

    // First, connect to master database to create the main database
    const masterConfig = {
      server: process.env.DB_SERVER || 'DESKTOP-DPNS718\\SQLEXPRESS',
      database: 'master',
      user: process.env.DB_USER || 'OfficeEcommerceUser',
      password: process.env.DB_PASSWORD || 'OfficeApp123!',
      options: {
        encrypt: false,
        trustServerCertificate: true,
        enableArithAbort: true,
        requestTimeout: 30000,
        connectionTimeout: 30000,
        instanceName: 'SQLEXPRESS'
      }
    };

    logger.info('Connecting to master database...');
    const masterPool = await sql.connect(masterConfig);
    logger.info('Connected to master database');

    // Check if database exists, create if not
    const dbCheckQuery = `
      IF NOT EXISTS (SELECT name FROM sys.databases WHERE name = 'OfficeEcommerce')
      BEGIN
          CREATE DATABASE OfficeEcommerce;
          PRINT 'Database OfficeEcommerce created successfully';
      END
      ELSE
      BEGIN
          PRINT 'OfficeEcommerce database already exists';
      END
    `;

    await masterPool.request().query(dbCheckQuery);
    logger.info('OfficeEcommerce database ready');

    // Close master connection
    await masterPool.close();

    // Now connect to the OfficeEcommerce database
    const dbConfig = {
      server: process.env.DB_SERVER || 'DESKTOP-DPNS718\\SQLEXPRESS',
      database: 'OfficeEcommerce',
      user: process.env.DB_USER || 'OfficeEcommerceUser',
      password: process.env.DB_PASSWORD || 'OfficeApp123!',
      options: {
        encrypt: false,
        trustServerCertificate: true,
        enableArithAbort: true,
        requestTimeout: 30000,
        connectionTimeout: 30000,
        instanceName: 'SQLEXPRESS'
      }
    };

    logger.info('Testing connection to OfficeEcommerce database...');
    const dbPool = await sql.connect(dbConfig);
    
    // Test the connection
    await dbPool.request().query('SELECT 1 as test');
    logger.info('Connection to OfficeEcommerce database successful');
    
    await dbPool.close();
    logger.info('Database setup completed successfully');

  } catch (error) {
    logger.error('Database setup failed:', error);
    
    // Provide helpful error messages
    if (error.code === 'ELOGIN') {
      logger.error('Login failed. Please check:');
      logger.error('1. SQL Server is running');
      logger.error('2. SQL Server Authentication is enabled');
      logger.error('3. User credentials are correct');
      logger.error('4. User has necessary permissions');
    } else if (error.code === 'ECONNREFUSED' || error.code === 'ESOCKET') {
      logger.error('Connection refused. Please check:');
      logger.error('1. SQL Server is running');
      logger.error('2. SQL Server is listening on the correct port');
      logger.error('3. Windows Firewall is not blocking the connection');
      logger.error('4. SQL Server Browser service is running (for named instances)');
    } else if (error.code === 'EINSTLOOKUP') {
      logger.error('Instance lookup failed. Please check:');
      logger.error('1. SQL Server instance name is correct');
      logger.error('2. SQL Server Browser service is running');
      logger.error('3. Instance is configured to accept remote connections');
    }
    
    throw error;
  }
};

// Create database user if it doesn't exist
const createDatabaseUser = async () => {
  try {
    logger.info('Creating database user...');

    // Connect to master with admin credentials
    const adminConfig = {
      server: process.env.DB_SERVER || 'DESKTOP-DPNS718\\SQLEXPRESS',
      database: 'master',
      options: {
        encrypt: false,
        trustServerCertificate: true,
        enableArithAbort: true,
        requestTimeout: 30000,
        connectionTimeout: 30000,
        instanceName: 'SQLEXPRESS',
        integratedSecurity: true // Use Windows Authentication for admin tasks
      }
    };

    const adminPool = await sql.connect(adminConfig);

    // Create login if it doesn't exist
    const createLoginQuery = `
      IF NOT EXISTS (SELECT name FROM sys.server_principals WHERE name = 'OfficeEcommerceUser')
      BEGIN
          CREATE LOGIN OfficeEcommerceUser WITH PASSWORD = 'OfficeApp123!';
          PRINT 'Login OfficeEcommerceUser created successfully';
      END
      ELSE
      BEGIN
          PRINT 'Login OfficeEcommerceUser already exists';
      END
    `;

    await adminPool.request().query(createLoginQuery);

    // Switch to OfficeEcommerce database
    await adminPool.request().query('USE OfficeEcommerce');

    // Create user and assign permissions
    const createUserQuery = `
      IF NOT EXISTS (SELECT name FROM sys.database_principals WHERE name = 'OfficeEcommerceUser')
      BEGIN
          CREATE USER OfficeEcommerceUser FOR LOGIN OfficeEcommerceUser;
          ALTER ROLE db_owner ADD MEMBER OfficeEcommerceUser;
          PRINT 'User OfficeEcommerceUser created and granted permissions';
      END
      ELSE
      BEGIN
          PRINT 'User OfficeEcommerceUser already exists';
      END
    `;

    await adminPool.request().query(createUserQuery);
    await adminPool.close();

    logger.info('Database user setup completed');

  } catch (error) {
    logger.error('Failed to create database user:', error);
    logger.warn('You may need to create the user manually or run this script as administrator');
  }
};

// Command line interface
const main = async () => {
  const command = process.argv[2];
  
  switch (command) {
    case 'setup':
      await setupDatabase();
      break;
    case 'user':
      await createDatabaseUser();
      break;
    case 'all':
      await createDatabaseUser();
      await setupDatabase();
      break;
    default:
      console.log(`
Usage: node setup-database.js <command>

Commands:
  setup    Setup OfficeEcommerce database
  user     Create database user (requires admin privileges)
  all      Create user and setup database

Examples:
  node setup-database.js setup    # Setup database only
  node setup-database.js user     # Create database user
  node setup-database.js all      # Do everything
      `);
  }
};

// Handle unhandled promise rejections
process.on('unhandledRejection', (err) => {
  logger.error('Unhandled Promise Rejection:', err);
  process.exit(1);
});

// Run the script
if (require.main === module) {
  main();
}

module.exports = {
  setupDatabase,
  createDatabaseUser
};
