const fs = require('fs');
const path = require('path');
const { connectDB, executeQuery, closeDB } = require('../config/database');
const logger = require('../utils/logger');

// Migration script to set up the database
const runMigration = async () => {
  try {
    logger.info('Starting database migration...');

    // Connect to database
    await connectDB();
    logger.info('Connected to database');

    // Read and execute schema
    const schemaPath = path.join(__dirname, '../database/schema.sql');
    const schemaSQL = fs.readFileSync(schemaPath, 'utf8');
    
    logger.info('Executing database schema...');
    
    // Split the SQL file by GO statements and execute each batch
    const batches = schemaSQL.split(/\r?\nGO\r?\n/);

    for (let i = 0; i < batches.length; i++) {
      const batch = batches[i].trim();
      if (batch && batch.length > 0) {
        try {
          // Skip USE statements as we're already connected to the database
          if (batch.toUpperCase().startsWith('USE ')) {
            logger.info(`Skipped batch ${i + 1}/${batches.length} (USE statement)`);
            continue;
          }

          await executeQuery(batch);
          logger.info(`Executed batch ${i + 1}/${batches.length}`);
        } catch (error) {
          logger.error(`Error in batch ${i + 1}:`, error);
          logger.error(`Batch content: ${batch.substring(0, 200)}...`);
          throw error;
        }
      }
    }

    logger.info('Database schema created successfully');

    // Ask if user wants to load sample data
    const loadSampleData = process.argv.includes('--sample-data');
    
    if (loadSampleData) {
      logger.info('Loading sample data...');
      
      const sampleDataPath = path.join(__dirname, '../database/sample-data.sql');
      const sampleDataSQL = fs.readFileSync(sampleDataPath, 'utf8');
      
      // Split and execute sample data batches
      const sampleBatches = sampleDataSQL.split(/\r?\nGO\r?\n/);
      
      for (let i = 0; i < sampleBatches.length; i++) {
        const batch = sampleBatches[i].trim();
        if (batch) {
          try {
            await executeQuery(batch);
            logger.info(`Executed sample data batch ${i + 1}/${sampleBatches.length}`);
          } catch (error) {
            logger.error(`Error in sample data batch ${i + 1}:`, error);
            throw error;
          }
        }
      }
      
      logger.info('Sample data loaded successfully');
    }

    logger.info('Migration completed successfully!');
    
  } catch (error) {
    logger.error('Migration failed:', error);
    process.exit(1);
  } finally {
    await closeDB();
  }
};

// Check database connection
const checkConnection = async () => {
  try {
    await connectDB();
    const result = await executeQuery('SELECT 1 as test');
    logger.info('Database connection test successful');
    return true;
  } catch (error) {
    logger.error('Database connection failed:', error);
    return false;
  } finally {
    await closeDB();
  }
};

// Drop all tables (use with caution)
const dropAllTables = async () => {
  try {
    logger.warn('WARNING: This will drop all tables and data!');
    
    await connectDB();
    
    const dropSQL = `
      -- Drop all foreign key constraints first
      DECLARE @sql NVARCHAR(MAX) = '';
      SELECT @sql = @sql + 'ALTER TABLE ' + QUOTENAME(SCHEMA_NAME(schema_id)) + '.' + QUOTENAME(name) + ' DROP CONSTRAINT ' + QUOTENAME(fk.name) + ';' + CHAR(13)
      FROM sys.tables t
      INNER JOIN sys.foreign_keys fk ON t.object_id = fk.parent_object_id
      WHERE t.is_ms_shipped = 0;
      EXEC sp_executesql @sql;

      -- Drop all tables
      SET @sql = '';
      SELECT @sql = @sql + 'DROP TABLE ' + QUOTENAME(SCHEMA_NAME(schema_id)) + '.' + QUOTENAME(name) + ';' + CHAR(13)
      FROM sys.tables
      WHERE is_ms_shipped = 0;
      EXEC sp_executesql @sql;

      -- Drop all views
      SET @sql = '';
      SELECT @sql = @sql + 'DROP VIEW ' + QUOTENAME(SCHEMA_NAME(schema_id)) + '.' + QUOTENAME(name) + ';' + CHAR(13)
      FROM sys.views
      WHERE is_ms_shipped = 0;
      EXEC sp_executesql @sql;

      -- Drop all stored procedures
      SET @sql = '';
      SELECT @sql = @sql + 'DROP PROCEDURE ' + QUOTENAME(SCHEMA_NAME(schema_id)) + '.' + QUOTENAME(name) + ';' + CHAR(13)
      FROM sys.procedures
      WHERE is_ms_shipped = 0;
      EXEC sp_executesql @sql;
    `;
    
    await executeQuery(dropSQL);
    logger.info('All tables, views, and procedures dropped successfully');
    
  } catch (error) {
    logger.error('Error dropping tables:', error);
    throw error;
  } finally {
    await closeDB();
  }
};

// Command line interface
const main = async () => {
  const command = process.argv[2];
  
  switch (command) {
    case 'up':
      await runMigration();
      break;
    case 'check':
      const isConnected = await checkConnection();
      process.exit(isConnected ? 0 : 1);
      break;
    case 'drop':
      if (process.argv.includes('--confirm')) {
        await dropAllTables();
      } else {
        logger.warn('Use --confirm flag to drop all tables: npm run migrate drop --confirm');
      }
      break;
    case 'reset':
      if (process.argv.includes('--confirm')) {
        await dropAllTables();
        await runMigration();
      } else {
        logger.warn('Use --confirm flag to reset database: npm run migrate reset --confirm');
      }
      break;
    default:
      console.log(`
Usage: node migrate.js <command> [options]

Commands:
  up              Run database migration (create tables, views, procedures)
  check           Check database connection
  drop --confirm  Drop all tables, views, and procedures
  reset --confirm Drop and recreate everything

Options:
  --sample-data   Load sample data after migration (use with 'up' command)
  --confirm       Confirm destructive operations

Examples:
  node migrate.js up                    # Create database schema
  node migrate.js up --sample-data      # Create schema and load sample data
  node migrate.js check                 # Test database connection
  node migrate.js drop --confirm        # Drop all tables
  node migrate.js reset --confirm       # Reset database completely
      `);
  }
};

// Handle unhandled promise rejections
process.on('unhandledRejection', (err) => {
  logger.error('Unhandled Promise Rejection:', err);
  process.exit(1);
});

// Run the script
if (require.main === module) {
  main();
}

module.exports = {
  runMigration,
  checkConnection,
  dropAllTables
};
