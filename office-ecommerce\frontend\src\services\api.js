// API service for frontend application
// This handles communication with the backend API
import apiClient from './apiClient';



// Enhanced Products API for admin functionality
const productsApi = {
  // Get products with pagination and filtering
  getProducts: async (params = {}) => {
    try {
      const response = await apiClient.get('/api/products', { params });
      return response;
    } catch (error) {
      // Return empty data structure if backend is not available
      return {
        success: false,
        data: {
          products: [],
          pagination: {
            currentPage: 1,
            totalPages: 0,
            totalItems: 0,
            itemsPerPage: 20
          }
        },
        error: error.message
      };
    }
  },

  // Get product by ID with full details
  getProductById: async (id) => {
    try {
      const response = await apiClient.get(`/api/products/${id}`);
      return response;
    } catch (error) {
      return {
        success: false,
        message: 'Product not found',
        error: error.message
      };
    }
  },

  // Create or update product
  createOrUpdateProduct: async (productData) => {
    const url = productData.ProductID
      ? `/api/products/${productData.ProductID}`
      : '/api/products';

    const method = productData.ProductID ? 'PUT' : 'POST';
    return await apiClient[method.toLowerCase()](url, productData);
  },

  // Delete product
  deleteProduct: async (productId) => {
    return await apiClient.delete(`/api/products/${productId}`);
  },

  // Upload 3D model
  uploadModel: async (productId, formData) => {
    return await apiClient.post(`/api/products/${productId}/models`, formData, {
      headers: {
        'Content-Type': 'multipart/form-data'
      }
    });
  },

  // Upload images
  uploadImages: async (productId, formData) => {
    return await apiClient.post(`/api/products/${productId}/images`, formData, {
      headers: {
        'Content-Type': 'multipart/form-data'
      }
    });
  },

  // Get categories
  getCategories: async () => {
    try {
      const response = await apiClient.get('/api/products/categories');
      return response;
    } catch (error) {
      return {
        success: false,
        data: [],
        error: error.message
      };
    }
  }
};

export default api;
