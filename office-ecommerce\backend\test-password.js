const bcrypt = require('bcryptjs');

async function testPassword() {
  const password = 'admin123';
  const hash = '$2a$12$LQv3c1yqBw2fyuDcj2ufaOEFvq/RgHQQvUbCJ3ixfCW5A6T8S8jjO';
  
  console.log('Testing password:', password);
  console.log('Against hash:', hash);
  
  const isMatch = await bcrypt.compare(password, hash);
  console.log('Password match:', isMatch);
  
  // Let's also test generating a new hash for admin123
  const newHash = await bcrypt.hash('admin123', 12);
  console.log('New hash for admin123:', newHash);
  
  const newMatch = await bcrypt.compare('admin123', newHash);
  console.log('New hash match:', newMatch);
}

testPassword();
