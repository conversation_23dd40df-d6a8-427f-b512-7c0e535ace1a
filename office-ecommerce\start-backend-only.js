const { spawn } = require('child_process');
const path = require('path');
const fs = require('fs');
const axios = require('axios');

/**
 * Start Backend Only Script
 * Starts just the backend server with proper configuration
 */

console.log('🚀 Starting Backend Server...\n');

// Ensure backend environment is set up
const setupBackendEnv = () => {
  console.log('📝 Setting up backend environment...');
  
  const envPath = path.join(__dirname, 'backend', '.env');
  const envContent = `# Backend Configuration
PORT=5001
NODE_ENV=development
FRONTEND_URL=http://localhost:3000

# JWT Configuration
JWT_SECRET=office-ecommerce-jwt-secret-key-2024
JWT_EXPIRES_IN=24h

# Database Configuration (optional for now)
DB_ENABLED=false

# Logging
LOG_LEVEL=info
LOG_FILE=logs/app.log

# PayMongo (optional)
PAYMONGO_SECRET_KEY=sk_test_x4JDky9QoB7AomXCvPYdJs5W
PAYMONGO_PUBLIC_KEY=pk_test_MdQNPhNfqdixDE7V8qW9zbxG
`;
  
  fs.writeFileSync(envPath, envContent);
  console.log('✅ Backend environment configured');
};

// Start the backend server
const startBackend = () => {
  return new Promise((resolve, reject) => {
    console.log('🚀 Starting backend server on port 5001...');
    
    const backendDir = path.join(__dirname, 'backend');
    const backendProcess = spawn('node', ['server.js'], {
      cwd: backendDir,
      stdio: 'pipe',
      shell: true
    });

    let serverStarted = false;

    backendProcess.stdout.on('data', (data) => {
      const output = data.toString().trim();
      if (output) {
        console.log(`[Backend] ${output}`);
        
        if (output.includes('Server running on port') || output.includes('running on port 5001')) {
          if (!serverStarted) {
            serverStarted = true;
            console.log('✅ Backend server started successfully!');
            resolve(backendProcess);
          }
        }
      }
    });

    backendProcess.stderr.on('data', (data) => {
      const output = data.toString().trim();
      if (output && !output.includes('DeprecationWarning')) {
        console.log(`[Backend Error] ${output}`);
      }
    });

    backendProcess.on('close', (code) => {
      console.log(`Backend process exited with code ${code}`);
      if (code !== 0 && !serverStarted) {
        reject(new Error(`Backend failed to start (exit code: ${code})`));
      }
    });

    backendProcess.on('error', (error) => {
      console.error('Failed to start backend:', error);
      reject(error);
    });

    // Timeout after 30 seconds
    setTimeout(() => {
      if (!serverStarted) {
        console.log('⚠️ Backend startup timeout - but may still be starting...');
        resolve(backendProcess);
      }
    }, 30000);
  });
};

// Test if backend is responding
const testBackend = async () => {
  console.log('🔍 Testing backend connectivity...');
  
  for (let i = 0; i < 10; i++) {
    try {
      const response = await axios.get('http://localhost:5001/health', { timeout: 2000 });
      if (response.status === 200) {
        console.log('✅ Backend is responding correctly!');
        console.log(`📊 Response: ${JSON.stringify(response.data, null, 2)}`);
        return true;
      }
    } catch (error) {
      if (i < 9) {
        console.log(`⏳ Waiting for backend... (attempt ${i + 1}/10)`);
        await new Promise(resolve => setTimeout(resolve, 2000));
      }
    }
  }
  
  console.log('⚠️ Backend health check failed, but server may still be running');
  return false;
};

// Main execution
const main = async () => {
  try {
    // Setup environment
    setupBackendEnv();
    
    // Start backend
    const backendProcess = await startBackend();
    
    // Wait a bit for server to stabilize
    await new Promise(resolve => setTimeout(resolve, 3000));
    
    // Test backend
    await testBackend();
    
    console.log('\n🎉 Backend is ready!');
    console.log('🔧 Backend API: http://localhost:5001');
    console.log('❤️ Health Check: http://localhost:5001/health');
    console.log('📋 API Health: http://localhost:5001/api/health');
    console.log('📦 Products: http://localhost:5001/api/products');
    console.log('\n💡 You can now start the frontend separately');
    console.log('⏹️ Press Ctrl+C to stop the backend server');
    
    // Handle graceful shutdown
    process.on('SIGINT', () => {
      console.log('\n🛑 Shutting down backend...');
      backendProcess.kill('SIGTERM');
      setTimeout(() => {
        process.exit(0);
      }, 2000);
    });
    
    // Keep process alive
    process.stdin.resume();
    
  } catch (error) {
    console.error('❌ Failed to start backend:', error.message);
    console.log('\n🔧 Troubleshooting:');
    console.log('1. Make sure Node.js is installed');
    console.log('2. Run "npm install" in the backend directory');
    console.log('3. Check if port 5001 is available');
    console.log('4. Check backend/logs/app.log for detailed errors');
    process.exit(1);
  }
};

// Run the script
main();
