const axios = require('axios');

async function testAuth() {
  try {
    console.log('Testing authentication...');
    
    const response = await axios.post('http://localhost:5001/api/auth/login', {
      email: '<EMAIL>',
      password: 'admin123'
    });
    
    console.log('✅ Authentication successful!');
    console.log('Response:', JSON.stringify(response.data, null, 2));

    // Decode the JWT token to see what's inside
    const jwt = require('jsonwebtoken');
    const token = response.data.data.token;
    const decoded = jwt.decode(token);
    console.log('Decoded JWT token:', JSON.stringify(decoded, null, 2));
    
  } catch (error) {
    console.log('❌ Authentication failed!');
    if (error.response) {
      console.log('Status:', error.response.status);
      console.log('Response:', JSON.stringify(error.response.data, null, 2));
    } else {
      console.log('Error:', error.message);
    }
  }
}

testAuth();
