const { connectDB } = require('./config/database');
const Order = require('./models/Order');

async function testOrderModel() {
  try {
    console.log('🔍 Testing Order Model...');

    // Connect to database
    console.log('\n📊 Connecting to database...');
    await connectDB();
    console.log('✅ Database connected');

    // Create order model instance
    console.log('\n🔍 Creating Order model instance...');
    const orderModel = new Order();
    console.log('✅ Order model created');

    // Check if the method exists
    console.log('\n🔍 Checking if getOrdersWithPagination method exists...');
    console.log('Method exists:', typeof orderModel.getOrdersWithPagination);
    console.log('Available methods:', Object.getOwnPropertyNames(Object.getPrototypeOf(orderModel)).filter(name => typeof orderModel[name] === 'function'));

    // Test the method
    if (typeof orderModel.getOrdersWithPagination === 'function') {
      console.log('\n🔍 Testing getOrdersWithPagination method...');
      try {
        const result = await orderModel.getOrdersWithPagination({
          page: 1,
          limit: 10
        });
        console.log('✅ Method call successful');
        console.log('Result:', JSON.stringify(result, null, 2));
      } catch (error) {
        console.log('❌ Method call failed:', error.message);
        console.log('Stack:', error.stack);
      }
    } else {
      console.log('❌ Method does not exist');
    }

    console.log('\n✅ Test completed!');

  } catch (error) {
    console.error('❌ Test failed:', error.message);
    console.error('Stack:', error.stack);
  }
}

// Run the test
testOrderModel();
